"""
Flask Web应用主文件
"""
from flask import Flask, render_template, request, jsonify, send_file, session, Response
import os
import json
import uuid
import threading
import time
from datetime import datetime

# 加载环境变量
try:
    from dotenv import load_dotenv
    load_dotenv()
    print("✅ 成功加载.env文件")
except ImportError:
    print("⚠️ python-dotenv未安装，将直接使用系统环境变量")
except Exception as e:
    print(f"⚠️ 加载.env文件失败: {e}")
try:
    import pandas as pd
    HAS_PANDAS = True
except ImportError:
    HAS_PANDAS = False
    print("警告: pandas未安装，Excel导出功能将受限")

try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False
    print("警告: openai未安装，AI分析功能将受限")

from database import DatabaseManager
from web_spider import WebSpider

app = Flask(__name__)
app.secret_key = 'cninfo_spider_secret_key_2024'

# OpenAI配置
OPENAI_API_KEY = os.getenv('OPENAI_API_KEY', '')
OPENAI_BASE_URL = os.getenv('OPENAI_BASE_URL', 'https://api.openai.com/v1')
OPENAI_MODEL = os.getenv('OPENAI_MODEL', 'gpt-3.5-turbo')

# 打印配置信息（用于调试）
print("🔧 OpenAI配置检查:")
print(f"  - HAS_OPENAI: {HAS_OPENAI}")
print(f"  - OPENAI_API_KEY: {'已设置' if OPENAI_API_KEY else '未设置'} (长度: {len(OPENAI_API_KEY)})")
print(f"  - OPENAI_BASE_URL: {OPENAI_BASE_URL}")
print(f"  - OPENAI_MODEL: {OPENAI_MODEL}")

# 配置OpenAI客户端
if HAS_OPENAI and OPENAI_API_KEY:
    try:
        openai.api_key = OPENAI_API_KEY
        if OPENAI_BASE_URL != 'https://api.openai.com/v1':
            openai.api_base = OPENAI_BASE_URL
        print(f"✅ OpenAI配置成功: {OPENAI_BASE_URL}, 模型: {OPENAI_MODEL}")
    except Exception as e:
        print(f"❌ OpenAI配置失败: {e}")
else:
    if not HAS_OPENAI:
        print("⚠️ openai库未安装，将使用模拟AI响应")
    elif not OPENAI_API_KEY:
        print("⚠️ OPENAI_API_KEY未配置，将使用模拟AI响应")
    else:
        print("⚠️ OpenAI配置异常，将使用模拟AI响应")

# 初始化数据库和爬虫
# 自动检测环境并设置合适的路径
def get_database_path():
    """自动检测环境并返回合适的数据库路径"""
    # 检查是否在Docker容器中
    if os.path.exists('/.dockerenv'):
        # Docker环境
        db_path = 'data/database/cninfo_reports.db'
        print("🐳 检测到Docker环境")
    else:
        # 本地环境，检查多个可能的路径
        possible_paths = [
            'cninfo_reports.db',  # 当前目录
            'database/cninfo_reports.db',  # database子目录
            '../cninfo_reports.db',  # 上级目录（cninfo_process目录）
            'data/database/cninfo_reports.db',  # Docker挂载路径
        ]

        db_path = None
        print(f"🔍 正在检查数据库路径...")
        for path in possible_paths:
            print(f"  检查: {path} -> {'存在' if os.path.exists(path) else '不存在'}")
            if os.path.exists(path):
                db_path = path
                print(f"💻 本地环境，找到数据库: {path}")
                break

        if not db_path:
            # 如果都没找到，使用默认路径
            db_path = 'cninfo_reports.db'
            print(f"💻 本地环境，使用默认路径: {db_path}")

    return db_path

# 确保必要目录存在
os.makedirs('data/database', exist_ok=True)
os.makedirs('txt', exist_ok=True)
os.makedirs('pdf', exist_ok=True)

# 获取数据库路径并初始化
db_path = get_database_path()
db_manager = DatabaseManager(db_path=db_path)
spider = WebSpider(db_manager)

# 全局任务状态存储
task_status = {}

def auto_import_txt_files():
    """容器启动时自动导入TXT文件"""
    try:
        txt_dir = 'txt'
        if not os.path.exists(txt_dir):
            print("📁 TXT目录不存在，跳过自动导入")
            return

        # 检查数据库中是否已有数据
        existing_reports = db_manager.get_all_reports()
        if existing_reports:
            print(f"📊 数据库中已有 {len(existing_reports)} 条年报记录，跳过自动导入")
            return

        # 获取TXT文件列表
        txt_files = [f for f in os.listdir(txt_dir) if f.endswith('.txt')]
        if not txt_files:
            print("📁 TXT目录为空，跳过自动导入")
            return

        print(f"🚀 检测到 {len(txt_files)} 个TXT文件，开始自动导入...")

        imported = 0
        skipped = 0
        errors = 0

        for txt_file in txt_files:
            try:
                file_path = os.path.join(txt_dir, txt_file)

                # 解析文件名获取信息
                parts = txt_file.replace('.txt', '').split('_')
                if len(parts) >= 3:
                    stock_code = parts[0]
                    company_name = parts[1]
                    report_title = '_'.join(parts[2:])
                else:
                    stock_code = 'Unknown'
                    company_name = 'Unknown'
                    report_title = txt_file.replace('.txt', '')

                # 检查是否已存在
                if db_manager.report_exists(stock_code, txt_file):
                    skipped += 1
                    continue

                # 读取文件内容
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                if content.strip():
                    # 添加到数据库
                    db_manager.add_report(
                        stock_code=stock_code,
                        company_name=company_name,
                        report_title=report_title,
                        file_name=txt_file,
                        file_path=file_path,
                        txt_content=content
                    )
                    imported += 1

                    # 添加公司信息（如果不存在）
                    try:
                        db_manager.add_company(stock_code, company_name, '')
                    except:
                        pass  # 公司可能已存在
                else:
                    errors += 1

            except Exception as e:
                print(f"❌ 导入文件失败 {txt_file}: {e}")
                errors += 1

        print(f"✅ 自动导入完成: 新增 {imported} 个，跳过 {skipped} 个，失败 {errors} 个")

    except Exception as e:
        print(f"❌ 自动导入过程出错: {e}")

# 容器启动时自动导入TXT文件
print("🐳 Docker容器启动中...")
auto_import_txt_files()


@app.route('/')
def index():
    """主页"""
    return render_template('index.html')


@app.route('/api/companies')
def get_companies():
    """获取公司列表"""
    companies = db_manager.get_companies()
    return jsonify({
        'success': True,
        'data': companies
    })


@app.route('/api/import_txt', methods=['POST'])
def import_txt_files():
    """导入txt文件到数据库"""
    try:
        txt_dir = request.json.get('txt_dir', 'txt')
        result = db_manager.import_txt_files(txt_dir)

        # 构建详细的消息
        message_parts = []
        if result['imported'] > 0:
            message_parts.append(f"成功导入 {result['imported']} 个文件")
        if result['skipped'] > 0:
            message_parts.append(f"跳过重复 {result['skipped']} 个文件")
        if result['errors'] > 0:
            message_parts.append(f"失败 {result['errors']} 个文件")

        message = "，".join(message_parts) if message_parts else "没有文件需要处理"

        return jsonify({
            'success': True,
            'message': f'导入完成：{message}',
            'imported': result['imported'],
            'skipped': result['skipped'],
            'errors': result['errors'],
            'total': result['imported'] + result['skipped'] + result['errors']
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导入失败: {str(e)}'
        })


@app.route('/api/search_reports', methods=['POST'])
def search_reports():
    """搜索年报"""
    try:
        data = request.json
        keyword = data.get('keyword')
        stock_codes = data.get('stock_codes', [])
        year = data.get('year')
        
        reports = db_manager.search_reports(keyword, stock_codes, year)
        return jsonify({
            'success': True,
            'data': reports
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'搜索失败: {str(e)}'
        })


@app.route('/api/start_analysis', methods=['POST'])
def start_analysis():
    """开始分析任务"""
    try:
        data = request.json
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        search_keyword = data.get('search_keyword', '年度报告')
        start_date = data.get('start_date', '2024-01-01')
        end_date = data.get('end_date', '2025-12-31')
        use_online = data.get('use_online', True)
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]

        
        if not stock_codes:
            return jsonify({
                'success': False,
                'message': '请输入股票代码'
            })
        
        if not keywords:
            return jsonify({
                'success': False,
                'message': '请输入关键词'
            })
        
        # 生成任务ID
        task_id = str(uuid.uuid4())
        task_name = f"关键词分析_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        # 创建任务记录
        db_manager.create_analysis_task(task_id, task_name, keywords, stock_codes)
        
        # 初始化任务状态
        task_status[task_id] = {
            'status': 'running',
            'progress': 0,
            'message': '任务开始...',
            'current_step': 0,
            'total_steps': len(stock_codes),
            'results': None,
            'start_time': datetime.now().isoformat()
        }
        
        # 启动后台任务
        def run_analysis():
            def progress_callback(current, total, message):
                progress = int((current / total) * 100)
                task_status[task_id].update({
                    'progress': progress,
                    'message': message,
                    'current_step': current,
                    'total_steps': total
                })
                db_manager.update_task_progress(task_id, progress)
            
            try:
                results = spider.crawl_and_analyze(
                    stock_codes=stock_codes,
                    keywords=keywords,
                    search_keyword=search_keyword,
                    start_date=start_date,
                    end_date=end_date,
                    use_online=use_online,
                    task_id=task_id,
                    related_parties=related_parties,
                    innovation_keywords=keywords,
                    progress_callback=progress_callback
                )
                
                task_status[task_id].update({
                    'status': 'completed',
                    'progress': 100,
                    'message': '任务完成',
                    'results': results,
                    'end_time': datetime.now().isoformat()
                })
                
                db_manager.update_task_progress(task_id, 100, 'completed')
                
            except Exception as e:
                task_status[task_id].update({
                    'status': 'error',
                    'message': f'任务失败: {str(e)}',
                    'end_time': datetime.now().isoformat()
                })
                db_manager.update_task_progress(task_id, 0, 'error')
        
        # 启动线程
        thread = threading.Thread(target=run_analysis)
        thread.daemon = True
        thread.start()
        
        return jsonify({
            'success': True,
            'task_id': task_id,
            'message': '任务已启动'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'启动任务失败: {str(e)}'
        })


@app.route('/api/task_status/<task_id>')
def get_task_status(task_id):
    """获取任务状态"""
    if task_id in task_status:
        return jsonify({
            'success': True,
            'data': task_status[task_id]
        })
    else:
        # 从数据库获取任务状态
        task_info = db_manager.get_task_status(task_id)
        if task_info:
            return jsonify({
                'success': True,
                'data': {
                    'status': task_info['status'],
                    'progress': task_info['progress'],
                    'message': f"任务状态: {task_info['status']}",
                    'task_name': task_info['task_name']
                }
            })
        else:
            return jsonify({
                'success': False,
                'message': '任务不存在'
            })


@app.route('/api/stop_task/<task_id>', methods=['POST'])
def stop_task(task_id):
    """停止任务"""
    try:
        spider.stop_crawling()
        if task_id in task_status:
            task_status[task_id].update({
                'status': 'stopped',
                'message': '任务已停止',
                'end_time': datetime.now().isoformat()
            })
        
        db_manager.update_task_progress(task_id, 0, 'stopped')
        
        return jsonify({
            'success': True,
            'message': '任务已停止'
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'停止任务失败: {str(e)}'
        })


@app.route('/api/analysis_results/<task_id>')
def get_analysis_results(task_id):
    """获取分析结果"""
    try:
        if task_id in task_status and task_status[task_id].get('results'):
            results = task_status[task_id]['results']
            # 提取analysis_results部分
            if 'analysis_results' in results:
                return jsonify({
                    'success': True,
                    'data': results['analysis_results']
                })
            else:
                return jsonify({
                    'success': True,
                    'data': results
                })
        else:
            # 从数据库获取结果
            analysis_results = db_manager.get_keyword_analysis(task_id)
            return jsonify({
                'success': True,
                'data': analysis_results
            })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取结果失败: {str(e)}'
        })


@app.route('/api/export_filtered_results', methods=['POST'])
def export_filtered_results():
    """导出筛选后的结果"""
    try:
        data = request.json
        results_data = data.get('data', [])
        filters_info = data.get('filters', {})
        export_format = data.get('format', 'excel')
        timestamp = data.get('timestamp', datetime.now().isoformat())

        if not results_data:
            return jsonify({'success': False, 'message': '没有数据可导出'})

        print(f"📊 导出筛选结果: {len(results_data)} 条记录")

        # 创建DataFrame
        df = pd.DataFrame(results_data)

        # 确保列的顺序和中文标题
        column_mapping = {
            'stock_code': '股票代码',
            'company_name': '公司名称',
            'file_name': '文件名',
            'keyword': '关键词',
            'count': '出现次数',
            'analysis_date': '分析时间'
        }

        # 重新排列列并重命名
        available_columns = [col for col in column_mapping.keys() if col in df.columns]
        df = df[available_columns]
        df = df.rename(columns=column_mapping)

        # 格式化分析时间
        if '分析时间' in df.columns:
            df['分析时间'] = pd.to_datetime(df['分析时间']).dt.strftime('%Y-%m-%d %H:%M:%S')

        # 生成文件名部分
        filter_parts = []
        if filters_info.get('keywords'):
            filter_parts.append(f"关键词{len(filters_info['keywords'])}个")
        if filters_info.get('companies'):
            filter_parts.append(f"公司{len(filters_info['companies'])}个")
        if filters_info.get('minCount', 0) > 1:
            filter_parts.append(f"最小{filters_info['minCount']}次")
        if filters_info.get('hideZero'):
            filter_parts.append('隐藏0次')

        # 创建临时文件
        import tempfile
        import io

        # 根据格式选择导出方式
        if export_format == 'csv':
            # CSV格式导出
            output = io.StringIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')
            output.seek(0)

            # 生成文件名
            filter_suffix = '_'.join(filter_parts) if filter_parts else '全部'
            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"筛选结果_{filter_suffix}_{timestamp_str}.csv"

            print(f"✅ 筛选结果CSV导出成功: {filename}")

            return Response(
                output.getvalue(),
                mimetype='text/csv',
                headers={
                    'Content-Disposition': f'attachment; filename="{filename}"'
                }
            )

        else:
            # Excel格式导出
            # 使用内存缓冲区
            output = io.BytesIO()

            # 创建Excel文件
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 写入主数据
                df.to_excel(writer, sheet_name='筛选结果', index=False)

                # 添加筛选信息工作表
                filter_info_data = []
                if filters_info.get('keywords'):
                    filter_info_data.append(['筛选关键词', ', '.join(filters_info['keywords'])])
                if filters_info.get('companies'):
                    filter_info_data.append(['筛选公司', ', '.join(filters_info['companies'])])
                if filters_info.get('minCount', 0) > 1:
                    filter_info_data.append(['最小出现次数', str(filters_info['minCount'])])
                if filters_info.get('hideZero'):
                    filter_info_data.append(['隐藏零次记录', '是'])

                filter_info_data.append(['导出时间', datetime.now().strftime('%Y-%m-%d %H:%M:%S')])
                filter_info_data.append(['记录总数', str(len(results_data))])

                if filter_info_data:
                    filter_df = pd.DataFrame(filter_info_data, columns=['筛选条件', '值'])
                    filter_df.to_excel(writer, sheet_name='筛选信息', index=False)

                # 格式化主工作表
                worksheet = writer.sheets['筛选结果']

                # 设置列宽
                column_widths = {
                    'A': 12,  # 股票代码
                    'B': 25,  # 公司名称
                    'C': 35,  # 文件名
                    'D': 15,  # 关键词
                    'E': 12,  # 出现次数
                    'F': 20   # 分析时间
                }

                for col, width in column_widths.items():
                    worksheet.column_dimensions[col].width = width

                # 设置标题行样式
                from openpyxl.styles import Font, PatternFill, Alignment

                title_font = Font(bold=True, color='FFFFFF')
                title_fill = PatternFill(start_color='366092', end_color='366092', fill_type='solid')
                title_alignment = Alignment(horizontal='center', vertical='center')

                for cell in worksheet[1]:
                    cell.font = title_font
                    cell.fill = title_fill
                    cell.alignment = title_alignment

            output.seek(0)

            # 生成文件名
            filter_suffix = '_'.join(filter_parts) if filter_parts else '全部'
            timestamp_str = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"筛选结果_{filter_suffix}_{timestamp_str}.xlsx"

            print(f"✅ 筛选结果Excel导出成功: {filename}")

            return send_file(
                output,
                as_attachment=True,
                download_name=filename,
                mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )

    except Exception as e:
        print(f"❌ 导出筛选结果失败: {e}")
        return jsonify({'success': False, 'message': f'导出失败: {str(e)}'})


@app.route('/api/export_results/<task_id>')
def export_results(task_id):
    """导出分析结果为Excel"""
    try:
        analysis_results = db_manager.get_keyword_analysis(task_id)
        
        if not analysis_results:
            return jsonify({
                'success': False,
                'message': '没有找到分析结果'
            })
        
        if not HAS_PANDAS:
            return jsonify({
                'success': False,
                'message': 'pandas未安装，无法导出Excel文件'
            })

        # 按照新格式组织数据：公司名称作为行，关键词作为列
        # 首先获取所有唯一的关键词
        keywords = sorted(list(set(result['keyword'] for result in analysis_results)))

        # 按公司分组数据
        company_data = {}
        for result in analysis_results:
            company_name = result['company_name']  # 只使用公司名称作为key
            if company_name not in company_data:
                company_data[company_name] = {}
                # 初始化所有关键词为0
                for keyword in keywords:
                    company_data[company_name][keyword] = 0

            # 设置该关键词的出现次数
            company_data[company_name][result['keyword']] = result['count']

        # 转换为DataFrame格式
        df_data = []
        for company_name, data in company_data.items():
            row = {
                '公司名称': company_name  # 只显示公司名称
            }
            # 添加每个关键词的出现次数
            for keyword in keywords:
                row[keyword] = data[keyword]
            df_data.append(row)

        # 创建DataFrame，列顺序为：公司名称 + 关键词列
        columns = ['公司名称'] + keywords
        df = pd.DataFrame(df_data, columns=columns)

        # 保存Excel文件
        export_dir = 'exports'
        if not os.path.exists(export_dir):
            os.makedirs(export_dir)

        filename = f"analysis_results_{task_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        filepath = os.path.join(export_dir, filename)

        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='分析结果', index=False)

            # 获取工作表对象进行格式化
            worksheet = writer.sheets['分析结果']

            # 设置列宽
            worksheet.column_dimensions['A'].width = 30  # 公司名称列更宽

            # 设置关键词列宽度
            for i, keyword in enumerate(keywords, start=2):  # 从B列开始
                col_letter = chr(ord('A') + i - 1)  # 转换为Excel列字母
                if i <= 26:  # 只处理A-Z列
                    worksheet.column_dimensions[col_letter].width = 12
        
        return send_file(filepath, as_attachment=True, download_name=filename)
        
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'导出失败: {str(e)}'
        })


@app.route('/api/keyword_analysis', methods=['POST'])
def keyword_analysis_only():
    """仅进行关键词分析（使用本地数据）"""
    try:
        print("🔍 开始关键词分析...")
        data = request.json
        print(f"📥 接收到数据: {data}")

        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]

        print(f"📈 股票代码: {stock_codes}")
        print(f"🔤 关键词: {keywords}")
        print(f"👥 关联方: {related_parties}")
        print(f"💡 协同创新关键词: {keywords} (使用统计关键词)")

        if not stock_codes:
            print("❌ 没有股票代码")
            return jsonify({
                'success': False,
                'message': '请输入股票代码'
            })

        if not keywords:
            print("❌ 没有关键词")
            return jsonify({
                'success': False,
                'message': '请输入关键词'
            })

        # 获取本地年报数据
        print("📊 查询本地年报数据...")
        reports = db_manager.get_reports_by_stock_codes(stock_codes)
        print(f"📄 找到 {len(reports)} 个年报")

        if not reports:
            print("❌ 没有找到年报数据")
            return jsonify({
                'success': False,
                'message': '没有找到相关的年报数据，请先导入txt文件或在线下载'
            })

        # 生成分析ID
        analysis_id = f"keyword_analysis_{int(time.time())}"

        # 创建与"开始分析"一致的结果格式
        analysis_results = {}
        related_party_analysis = {}

        print("🔍 开始分析关键词...")
        # 分析每个年报
        for i, report in enumerate(reports, 1):
            print(f"[{i}/{len(reports)}] 分析: {report.get('file_name', 'Unknown')}")
            if report.get('txt_content'):
                keyword_stats = spider.analyze_keywords(report['txt_content'], keywords)
                print(f"  📊 关键词统计: {keyword_stats}")

                # 保存分析结果到数据库
                db_manager.save_keyword_analysis(
                    analysis_id, report['stock_code'], report['id'], keyword_stats
                )

                # 构建与"开始分析"一致的数据结构
                stock_code = report['stock_code']
                file_name = report.get('file_name', '')

                if stock_code not in analysis_results:
                    analysis_results[stock_code] = {}

                analysis_results[stock_code][file_name] = keyword_stats

                # 关联方分析
                if related_parties and keywords:
                    related_analysis = spider.analyze_related_parties(
                        report['txt_content'], related_parties, keywords
                    )
                    if related_analysis:
                        if stock_code not in related_party_analysis:
                            related_party_analysis[stock_code] = {}
                        related_party_analysis[stock_code][file_name] = related_analysis

        print("✅ 关键词分析完成")

        result = {
            'success': True,
            'analysis_id': analysis_id,
            'data': analysis_results,
            'related_party_analysis': related_party_analysis,
            'message': f'分析完成，共分析 {len(reports)} 个年报文件'
        }

        # 计算结果总数
        total_results = sum(len(files) for files in analysis_results.values())
        print(f"📤 准备返回 {total_results} 条结果")

        try:
            print("🔄 开始JSON序列化...")
            response = jsonify(result)
            print("✅ JSON序列化成功，准备返回响应")
            return response
        except Exception as json_error:
            print(f"❌ JSON序列化失败: {json_error}")
            return jsonify({
                'success': True,
                'analysis_id': analysis_id,
                'data': [],
                'message': f'分析完成但返回数据过大，请查看数据库结果'
            })

    except Exception as e:
        print(f"❌ 关键词分析失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'分析失败: {str(e)}'
        })


@app.route('/api/keyword_context/<analysis_id>/<keyword>', methods=['GET'])
def get_keyword_context(analysis_id, keyword):
    """获取关键词上下文片段"""
    try:
        print(f"🔍 获取上下文: analysis_id={analysis_id}, keyword={keyword}")

        # 获取上下文长度参数
        context_length = request.args.get('context_length', '100')
        try:
            context_length = int(context_length)
        except:
            context_length = 100

        print(f"📏 上下文长度设置: {context_length} 字符")

        # 获取过滤参数
        stock_code_filter = request.args.get('stock_code')
        file_name_filter = request.args.get('file_name')

        if stock_code_filter:
            print(f"🎯 只搜索股票代码: {stock_code_filter}")
        if file_name_filter:
            print(f"📄 只搜索文件: {file_name_filter}")

        # 从数据库获取分析结果
        analysis_results = db_manager.get_keyword_analysis(analysis_id)
        print(f"📊 找到 {len(analysis_results)} 条分析结果")

        contexts = []
        for result in analysis_results:
            # 应用过滤条件
            if result['keyword'] == keyword and result['count'] > 0:
                # 检查股票代码过滤
                if stock_code_filter and result.get('stock_code') != stock_code_filter:
                    continue

                # 检查文件名过滤
                if file_name_filter and result.get('file_name') != file_name_filter:
                    continue
                print(f"  🎯 匹配关键词: {result['stock_code']} - {result.get('file_name', 'Unknown')} - {keyword} ({result['count']}次)")

                # 直接通过report_id获取年报内容
                report_id = result.get('report_id')
                if report_id:
                    print(f"  📄 查找年报ID: {report_id}")
                    report = db_manager.get_report_by_id(report_id)

                    if report and report.get('txt_content'):
                        print(f"    ✅ 找到年报，文本长度: {len(report['txt_content'])}")

                        # 验证关键词确实存在
                        import re
                        clean_content = re.sub(r'[^\u4e00-\u9fa5]', '', report['txt_content'])
                        actual_count = clean_content.count(keyword)
                        print(f"    🔍 验证关键词'{keyword}'在清理后文本中的实际出现次数: {actual_count}")

                        # 提取关键词上下文（使用用户指定的长度）
                        context_snippets = extract_keyword_context(report['txt_content'], keyword, context_length)
                        if context_snippets:
                            contexts.append({
                                'stock_code': result['stock_code'],
                                'company_name': result.get('company_name', ''),
                                'file_name': result.get('file_name', ''),
                                'snippets': context_snippets,  # 返回所有片段
                                'total_count': len(context_snippets),
                                'keyword_count': result['count']
                            })
                            print(f"    📝 添加了 {len(context_snippets)} 个上下文片段")
                        else:
                            print(f"    ⚠️ 关键词'{keyword}'上下文提取失败")
                            # 显示文本开头用于调试
                            text_preview = report['txt_content'][:200].replace('\n', ' ')
                            print(f"    📄 文本开头预览: {text_preview}...")
                    elif report:
                        print(f"    ⚠️ 年报没有文本内容")
                    else:
                        print(f"    ❌ 未找到ID为{report_id}的年报")
                else:
                    print(f"    ❌ 分析结果中没有report_id")

        # 计算总片段数
        total_snippets = sum(len(ctx.get('snippets', [])) for ctx in contexts)

        if stock_code_filter or file_name_filter:
            filter_info = []
            if stock_code_filter:
                filter_info.append(f"股票:{stock_code_filter}")
            if file_name_filter:
                filter_info.append(f"文件:{file_name_filter}")
            print(f"🎉 在过滤条件({', '.join(filter_info)})下找到 {len(contexts)} 个文件，共 {total_snippets} 个上下文片段")
        else:
            print(f"🎉 总共找到 {len(contexts)} 个文件，共 {total_snippets} 个上下文片段")

        return jsonify({
            'success': True,
            'keyword': keyword,
            'contexts': contexts
        })

    except Exception as e:
        print(f"❌ 获取上下文失败: {e}")
        import traceback
        traceback.print_exc()
        return jsonify({
            'success': False,
            'message': f'获取上下文失败: {str(e)}'
        })


def extract_keyword_context(text, keyword, context_length=200):
    """提取关键词上下文片段"""
    import re

    print(f"    🔧 extract_keyword_context调用: keyword='{keyword}', context_length={context_length}")

    # 使用与关键词统计相同的文本清理方式
    clean_text_for_search = re.sub(r'[^\u4e00-\u9fa5]', '', text)

    # 找到所有关键词位置（在清理后的文本中）
    keyword_positions = []
    start = 0
    while True:
        pos = clean_text_for_search.find(keyword, start)
        if pos == -1:
            break
        keyword_positions.append(pos)
        start = pos + 1

    print(f"    🔍 在清理后的文本中找到 {len(keyword_positions)} 个'{keyword}'位置")

    if not keyword_positions:
        return []

    # 为了显示上下文，我们需要在原始文本中找到对应位置
    # 创建一个映射，从清理后的位置映射到原始文本位置
    original_text = text
    clean_to_original_map = []

    for i, char in enumerate(original_text):
        if re.match(r'[\u4e00-\u9fa5]', char):  # 如果是中文字符
            clean_to_original_map.append(i)

    # 提取上下文片段（提取所有位置）
    snippets = []
    for clean_pos in keyword_positions:  # 提取所有位置
        if clean_pos < len(clean_to_original_map) and clean_pos + len(keyword) <= len(clean_to_original_map):
            # 找到原始文本中关键词的开始和结束位置
            keyword_start = clean_to_original_map[clean_pos]
            keyword_end = clean_to_original_map[clean_pos + len(keyword) - 1] + 1

            # 在原始文本中提取上下文
            start_pos = max(0, keyword_start - context_length)
            end_pos = min(len(original_text), keyword_end + context_length)

            snippet = original_text[start_pos:end_pos]

            # 清理snippet中的多余空白
            snippet = re.sub(r'\s+', ' ', snippet).strip()

            # 智能高亮关键词（处理中间有符号的情况）
            highlighted_snippet = highlight_keyword_in_text(snippet, keyword)

            # 添加省略号
            if start_pos > 0:
                highlighted_snippet = '...' + highlighted_snippet
            if end_pos < len(original_text):
                highlighted_snippet = highlighted_snippet + '...'

            snippets.append(highlighted_snippet)

    print(f"    📝 成功提取 {len(snippets)} 个上下文片段")
    return snippets


def highlight_keyword_in_text(text, keyword):
    """在文本中智能高亮关键词，处理中间有符号的情况"""
    import re

    # 如果关键词长度小于2，使用简单替换
    if len(keyword) < 2:
        return text.replace(keyword, f'<mark class="bg-warning">{keyword}</mark>')

    # 先尝试简单匹配（完全相同）
    if keyword in text:
        simple_highlighted = text.replace(keyword, f'<mark class="bg-warning">{keyword}</mark>')
        print(f"    ✅ 使用简单匹配高亮关键词'{keyword}'")
        return simple_highlighted

    # 如果简单匹配失败，使用智能匹配
    print(f"    🔍 关键词'{keyword}'未找到完全匹配，尝试智能匹配...")

    # 创建关键词的正则表达式模式
    # 在每个字符之间允许插入少量非中文字符
    keyword_chars = list(keyword)
    pattern_parts = []

    for i, char in enumerate(keyword_chars):
        # 转义特殊正则字符
        escaped_char = re.escape(char)
        pattern_parts.append(escaped_char)

        # 在字符之间（除了最后一个字符）添加可选的非中文字符匹配
        # 限制插入字符的数量，避免匹配过于宽泛
        if i < len(keyword_chars) - 1:
            pattern_parts.append(r'[^\u4e00-\u9fa5]{0,3}?')  # 最多3个非中文字符，非贪婪匹配

    pattern = ''.join(pattern_parts)

    print(f"    🎯 关键词'{keyword}'的智能匹配模式: {pattern}")

    def replace_match(match):
        matched_text = match.group(0)
        print(f"    🎯 匹配到文本片段: '{matched_text}'")
        return f'<mark class="bg-warning">{matched_text}</mark>'

    try:
        # 使用正则表达式进行替换
        highlighted_text = re.sub(pattern, replace_match, text)

        # 检查是否有高亮
        if '<mark class="bg-warning">' in highlighted_text:
            print(f"    ✅ 成功智能高亮关键词'{keyword}'")
            return highlighted_text
        else:
            print(f"    ⚠️ 智能匹配也未找到关键词'{keyword}'")
            return text

    except Exception as e:
        print(f"    ❌ 智能高亮失败: {e}")
        return text


def test_highlight_function():
    """测试高亮功能"""
    test_cases = [
        ("人工智能技术发展", "人工智能", "人工智能"),
        ("人工 智能技术发展", "人工智能", "人工 智能"),
        ("人工、智能技术发展", "人工智能", "人工、智能"),
        ("人工（智能）技术发展", "人工智能", "人工（智能）"),
        ("产学研合作", "产学研", "产学研"),
        ("产、学、研合作", "产学研", "产、学、研"),
    ]

    print("🧪 测试关键词高亮功能:")
    for text, keyword, expected in test_cases:
        result = highlight_keyword_in_text(text, keyword)
        print(f"  文本: {text}")
        print(f"  关键词: {keyword}")
        print(f"  结果: {result}")
        print(f"  预期包含: {expected}")
        print("  ---")


@app.route('/api/ai_analysis', methods=['POST'])
def ai_analysis():
    """AI分析接口"""
    try:
        print("🤖 开始AI分析...")
        data = request.json
        print(f"📥 接收到数据: {data}")

        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]
        prompt = data.get('prompt', '').strip()
        openai_config = data.get('openai_config', {})

        print(f"📈 股票代码: {stock_codes}")
        print(f"🔤 关键词: {keywords}")
        print(f"👥 关联方: {related_parties}")
        print(f"💬 AI提示词: {prompt}")

        if not stock_codes:
            return jsonify({
                'success': False,
                'message': '请输入股票代码'
            })

        if not keywords:
            return jsonify({
                'success': False,
                'message': '请输入统计关键词'
            })

        if not related_parties:
            return jsonify({
                'success': False,
                'message': '请输入关联方'
            })

        if not prompt:
            return jsonify({
                'success': False,
                'message': '请输入AI分析要求'
            })

        # 获取年报数据
        print("📊 查询本地年报数据...")
        print(f"🔍 查询股票代码: {stock_codes}")

        # 先检查数据库中有哪些股票代码
        all_reports = db_manager.get_all_reports()
        print(f"📊 数据库中总共有 {len(all_reports)} 个年报")

        if all_reports:
            available_codes = set(report['stock_code'] for report in all_reports)
            print(f"📈 数据库中可用的股票代码: {sorted(available_codes)}")

            # 检查请求的股票代码是否存在
            missing_codes = set(stock_codes) - available_codes
            if missing_codes:
                print(f"❌ 以下股票代码在数据库中不存在: {sorted(missing_codes)}")

            existing_codes = set(stock_codes) & available_codes
            if existing_codes:
                print(f"✅ 以下股票代码在数据库中存在: {sorted(existing_codes)}")
        else:
            print("❌ 数据库中没有任何年报数据")

        reports = db_manager.get_reports_by_stock_codes(stock_codes)
        print(f"📄 找到 {len(reports)} 个年报")

        if reports:
            for report in reports[:3]:  # 显示前3个报告的信息
                print(f"  📋 {report['stock_code']} - {report.get('company_name', 'Unknown')} - {report.get('file_name', 'Unknown')}")

        if not reports:
            # 提供更详细的错误信息
            if not all_reports:
                error_msg = '数据库中没有年报数据，请先导入txt文件或在线下载年报'
            else:
                available_codes_str = ', '.join(sorted(available_codes)) if all_reports else '无'
                error_msg = f'没有找到指定股票代码的年报数据。\n请求的代码: {", ".join(stock_codes)}\n数据库中可用的代码: {available_codes_str}'

            return jsonify({
                'success': False,
                'message': error_msg
            })

        # 执行AI分析
        analysis_result = perform_ai_analysis(reports, keywords, related_parties, prompt, openai_config)

        return jsonify({
            'success': True,
            'analysis_result': analysis_result,
            'message': f'AI分析完成，共分析 {len(reports)} 个年报文件'
        })

    except Exception as e:
        print(f"❌ AI分析失败: {e}")
        return jsonify({
            'success': False,
            'message': f'AI分析失败: {str(e)}'
        })


@app.route('/api/ai_analysis_stream', methods=['POST'])
def ai_analysis_stream():
    """真正的流式AI分析接口"""
    try:
        # 在请求上下文中完成所有数据处理
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': '无效的请求数据'})

        # 解析参数
        stock_codes = [code.strip() for code in data.get('stock_codes', '').split('\n') if code.strip()]
        keywords = [kw.strip() for kw in data.get('keywords', '').split('\n') if kw.strip()]
        related_parties = [rp.strip() for rp in data.get('related_parties', '').split('\n') if rp.strip()]
        prompt = data.get('prompt', '').strip()
        openai_config = data.get('openai_config', {})

        # 参数验证
        if not stock_codes or not keywords or not related_parties or not prompt:
            return jsonify({'success': False, 'message': '请填写完整的分析参数'})

        # 获取年报数据
        reports = db_manager.get_reports_by_stock_codes(stock_codes)
        if not reports:
            return jsonify({'success': False, 'message': '没有找到相关的年报数据'})

        # 执行上下文搜索
        contexts = []
        for report in reports:
            stock_code = report['stock_code']
            company_name = report.get('company_name', '')
            txt_content = report['txt_content']

            if not txt_content:
                continue

            for related_party in related_parties:
                party_contexts = find_related_party_contexts(txt_content, related_party, keywords)

                for context_info in party_contexts:
                    contexts.append({
                        'stock_code': stock_code,
                        'company_name': company_name,
                        'related_party': related_party,
                        'context': context_info['text'],
                        'keywords_found': context_info['keywords_found'],
                        'has_keywords': context_info['has_keywords'],
                        'context_type': 'with_keywords' if context_info['has_keywords'] else 'related_party_only'
                    })

        # 生成流式响应
        def generate_stream():
            try:
                yield f"data: {json.dumps({'type': 'status', 'message': '开始AI分析...'})}\n\n"
                yield f"data: {json.dumps({'type': 'status', 'message': f'找到 {len(reports)} 个年报'})}\n\n"
                yield f"data: {json.dumps({'type': 'contexts', 'data': contexts})}\n\n"
                yield f"data: {json.dumps({'type': 'status', 'message': f'找到 {len(contexts)} 个相关上下文，开始AI分析...'})}\n\n"

                # 生成AI响应（流式）
                if contexts:
                    yield from generate_ai_response_stream(contexts, prompt, openai_config)
                else:
                    yield f"data: {json.dumps({'type': 'ai_chunk', 'data': '未找到相关上下文，无法进行AI分析。'})}\n\n"

                yield f"data: {json.dumps({'type': 'complete', 'message': 'AI分析完成'})}\n\n"

            except Exception as e:
                print(f"❌ 流式AI分析失败: {e}")
                yield f"data: {json.dumps({'type': 'error', 'message': f'分析失败: {str(e)}'})}\n\n"

        return Response(
            generate_stream(),
            mimetype='text/plain',
            headers={
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Access-Control-Allow-Origin': '*'
            }
        )

    except Exception as e:
        print(f"❌ 流式AI分析初始化失败: {e}")
        return jsonify({'success': False, 'message': f'分析初始化失败: {str(e)}'})


@app.route('/api/test_openai_simple', methods=['POST'])
def test_openai_simple():
    """简单的OpenAI API测试"""
    try:
        data = request.json
        api_key = data.get('api_key', OPENAI_API_KEY)
        base_url = data.get('base_url', OPENAI_BASE_URL)
        model = data.get('model', OPENAI_MODEL)

        if not api_key:
            return jsonify({
                'success': False,
                'message': '没有可用的API Key'
            })

        print(f"🧪 简单OpenAI测试: {base_url}, 模型: {model}")

        # 确保base_url格式正确
        if not base_url.startswith('http'):
            base_url = 'https://' + base_url
        if not base_url.endswith('/v1'):
            if base_url.endswith('/'):
                base_url = base_url + 'v1'
            else:
                base_url = base_url + '/v1'

        # 临时设置API配置
        original_api_key = getattr(openai, 'api_key', None)
        original_api_base = getattr(openai, 'api_base', None)

        openai.api_key = api_key
        openai.api_base = base_url

        # 发送简单测试请求
        test_messages = [{"role": "user", "content": "请回复：测试成功"}]

        try:
            # 尝试流式请求
            print("🧪 测试流式API...")
            response = openai.ChatCompletion.create(
                model=model,
                messages=test_messages,
                max_tokens=50,
                temperature=0,
                stream=True
            )

            content_parts = []
            chunk_count = 0
            for chunk in response:
                chunk_count += 1
                print(f"🧪 测试块 {chunk_count}: {chunk}")
                if 'choices' in chunk and len(chunk['choices']) > 0:
                    delta = chunk['choices'][0].get('delta', {})
                    if 'content' in delta and delta['content']:
                        content_parts.append(delta['content'])

            full_content = ''.join(content_parts)

            # 恢复原始配置
            if original_api_key:
                openai.api_key = original_api_key
            if original_api_base:
                openai.api_base = original_api_base

            return jsonify({
                'success': True,
                'message': f'流式API测试成功',
                'response': full_content,
                'chunks_received': chunk_count,
                'content_length': len(full_content)
            })

        except Exception as stream_error:
            print(f"🧪 流式API测试失败: {stream_error}")

            # 尝试非流式请求
            try:
                print("🧪 测试非流式API...")
                response = openai.ChatCompletion.create(
                    model=model,
                    messages=test_messages,
                    max_tokens=50,
                    temperature=0
                )

                content = response.choices[0].message.content.strip()

                # 恢复原始配置
                if original_api_key:
                    openai.api_key = original_api_key
                if original_api_base:
                    openai.api_base = original_api_base

                return jsonify({
                    'success': True,
                    'message': f'非流式API测试成功（流式失败: {str(stream_error)}）',
                    'response': content,
                    'content_length': len(content)
                })

            except Exception as non_stream_error:
                # 恢复原始配置
                if original_api_key:
                    openai.api_key = original_api_key
                if original_api_base:
                    openai.api_base = original_api_base

                return jsonify({
                    'success': False,
                    'message': f'API测试失败 - 流式: {str(stream_error)}, 非流式: {str(non_stream_error)}'
                })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试异常: {str(e)}'
        })


@app.route('/api/debug_database', methods=['GET'])
def debug_database():
    """调试数据库状态"""
    try:
        # 获取所有年报
        all_reports = db_manager.get_all_reports()

        # 获取所有公司
        all_companies = db_manager.get_all_companies()

        # 统计信息
        stock_codes = set(report['stock_code'] for report in all_reports)

        debug_info = {
            'total_reports': len(all_reports),
            'total_companies': len(all_companies),
            'unique_stock_codes': len(stock_codes),
            'stock_codes_list': sorted(stock_codes),
            'sample_reports': all_reports[:5] if all_reports else [],
            'sample_companies': all_companies[:5] if all_companies else []
        }

        return jsonify({
            'success': True,
            'debug_info': debug_info
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'调试失败: {str(e)}'
        })


@app.route('/api/test_openai', methods=['POST'])
def test_openai():
    """测试OpenAI连接"""
    try:
        data = request.json
        use_default = data.get('use_default', False)

        if use_default:
            # 测试服务器默认配置
            if not HAS_OPENAI:
                return jsonify({
                    'success': False,
                    'message': 'openai库未安装，请先安装：pip install openai'
                })

            if not OPENAI_API_KEY:
                return jsonify({
                    'success': False,
                    'message': '服务器未配置OpenAI API Key，请在.env文件中设置OPENAI_API_KEY'
                })

            # 使用全局配置测试
            result = test_openai_connection(OPENAI_API_KEY, OPENAI_BASE_URL, OPENAI_MODEL)

            if result['success']:
                return jsonify({
                    'success': True,
                    'message': f"服务器默认配置连接成功！模型: {OPENAI_MODEL}, Base URL: {OPENAI_BASE_URL}, 响应: {result['response']}"
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f"服务器默认配置连接失败: {result['error']}"
                })
        else:
            # 测试用户提供的配置
            api_key = data.get('api_key', '').strip()
            base_url = data.get('base_url', '').strip()
            model = data.get('model', 'gpt-3.5-turbo')

            if not api_key:
                return jsonify({
                    'success': False,
                    'message': '请提供API Key'
                })

            if not base_url:
                return jsonify({
                    'success': False,
                    'message': '请提供Base URL'
                })

            # 测试OpenAI连接
            result = test_openai_connection(api_key, base_url, model)

            if result['success']:
                return jsonify({
                    'success': True,
                    'message': f"连接成功！模型: {model}, 响应: {result['response']}"
                })
            else:
                return jsonify({
                    'success': False,
                    'message': f"连接失败: {result['error']}"
                })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        })


@app.route('/api/clean_duplicates', methods=['POST'])
def clean_duplicates():
    """清理重复数据"""
    try:
        # 清理重复的年报记录
        reports_cleaned = db_manager.clean_duplicate_reports()

        # 清理重复的关键词分析记录
        analysis_cleaned = db_manager.clean_duplicate_keyword_analysis()

        return jsonify({
            'success': True,
            'message': f'清理完成：删除了 {reports_cleaned} 条重复年报记录，{analysis_cleaned} 条重复分析记录',
            'reports_cleaned': reports_cleaned,
            'analysis_cleaned': analysis_cleaned
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'清理失败: {str(e)}'
        })


@app.route('/api/test_highlight', methods=['POST'])
def test_highlight():
    """测试关键词高亮功能"""
    try:
        data = request.json
        text = data.get('text', '')
        keyword = data.get('keyword', '')

        if not text or not keyword:
            return jsonify({
                'success': False,
                'message': '请提供文本和关键词'
            })

        highlighted_text = highlight_keyword_in_text(text, keyword)

        return jsonify({
            'success': True,
            'original_text': text,
            'keyword': keyword,
            'highlighted_text': highlighted_text
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'测试失败: {str(e)}'
        })


def perform_ai_analysis(reports, keywords, related_parties, prompt, openai_config=None):
    """执行AI分析"""
    import re

    print("🔍 开始搜索关联方上下文...")
    contexts = []

    # 遍历每个年报
    for report in reports:
        stock_code = report['stock_code']
        company_name = report.get('company_name', '')
        txt_content = report['txt_content']

        if not txt_content:
            continue

        print(f"📄 分析年报: {stock_code} - {company_name}")
        print(f"    📊 年报文本长度: {len(txt_content)} 字符")

        # 为每个关联方搜索上下文
        for related_party in related_parties:
            print(f"  🔍 搜索关联方: {related_party}")

            # 搜索关联方在文本中的位置
            party_contexts = find_related_party_contexts(txt_content, related_party, keywords)

            if party_contexts:
                print(f"    ✅ 找到 {len(party_contexts)} 个相关上下文")
                for i, context_info in enumerate(party_contexts):
                    context_type = 'with_keywords' if context_info['has_keywords'] else 'related_party_only'
                    print(f"      📝 上下文 {i+1}: 长度={len(context_info['text'])}, 类型={context_type}, 关键词={context_info['keywords_found']}")
                    contexts.append({
                        'stock_code': stock_code,
                        'company_name': company_name,
                        'related_party': related_party,
                        'context': context_info['text'],
                        'keywords_found': context_info['keywords_found'],
                        'has_keywords': context_info['has_keywords'],
                        'context_type': context_type
                    })
            else:
                print(f"    ❌ 未找到相关上下文")

                # 调试：检查关联方是否存在于文本中
                import re
                clean_text = re.sub(r'[^\u4e00-\u9fa5]', '', txt_content)
                if related_party in clean_text:
                    print(f"    🔍 关联方'{related_party}'存在于文本中，但未找到任何上下文")
                else:
                    print(f"    ❌ 关联方'{related_party}'不存在于文本中")

    print(f"🎯 总共找到 {len(contexts)} 个相关上下文")

    # 构建AI分析结果
    analysis_result = {
        'contexts': contexts,
        'ai_response': generate_ai_response(contexts, prompt, openai_config) if contexts else "未找到相关上下文，无法进行AI分析。"
    }

    return analysis_result


def find_related_party_contexts(text, related_party, keywords, context_length=300):
    """在文本中查找关联方的上下文，返回所有包含关联方的上下文"""
    import re

    print(f"    🔧 find_related_party_contexts调用: related_party='{related_party}', keywords={keywords}")

    contexts = []

    # 清理文本，保留中文字符用于搜索
    clean_text = re.sub(r'[^\u4e00-\u9fa5]', '', text)
    original_text = text

    print(f"    📄 原始文本长度: {len(original_text)}, 清理后文本长度: {len(clean_text)}")

    # 查找关联方在清理后文本中的位置
    party_positions = []
    start = 0
    while True:
        pos = clean_text.find(related_party, start)
        if pos == -1:
            break
        party_positions.append(pos)
        start = pos + 1

    print(f"    🔍 找到关联方'{related_party}' {len(party_positions)} 个位置: {party_positions[:5]}...")  # 只显示前5个位置

    if not party_positions:
        print(f"    ❌ 未找到关联方'{related_party}'")
        return contexts

    # 创建清理文本到原始文本的位置映射
    clean_to_original_map = []
    for i, char in enumerate(original_text):
        if re.match(r'[\u4e00-\u9fa5]', char):
            clean_to_original_map.append(i)

    print(f"    🗺️ 位置映射表长度: {len(clean_to_original_map)}")

    # 为每个位置提取上下文
    all_contexts = 0
    for clean_pos in party_positions:
        if clean_pos < len(clean_to_original_map):
            # 找到原始文本中的位置
            original_pos = clean_to_original_map[clean_pos]

            # 提取上下文
            start_pos = max(0, original_pos - context_length)
            end_pos = min(len(original_text), original_pos + len(related_party) + context_length)

            context = original_text[start_pos:end_pos]

            # 清理上下文中的多余空白
            context = re.sub(r'\s+', ' ', context).strip()

            print(f"    📝 提取的上下文片段 (位置{clean_pos}): {context[:100]}...")

            # 检查上下文中是否包含关键词
            clean_context = re.sub(r'[^\u4e00-\u9fa5]', '', context)
            found_keywords = []
            for keyword in keywords:
                if keyword in clean_context:
                    found_keywords.append(keyword)

            print(f"    🔍 在上下文中找到关键词: {found_keywords}")

            # 添加所有包含关联方的上下文，标记是否包含关键词
            context_info = {
                'text': context,
                'has_keywords': len(found_keywords) > 0,
                'keywords_found': found_keywords
            }
            contexts.append(context_info)
            all_contexts += 1

            if found_keywords:
                print(f"    ✅ 添加包含关键词的上下文 #{all_contexts}")
            else:
                print(f"    📝 添加仅包含关联方的上下文 #{all_contexts}")

    print(f"    📊 总共找到 {len(contexts)} 个包含关联方的上下文")
    return contexts


def extract_keywords_from_context(context, keywords):
    """从上下文中提取包含的关键词"""
    import re

    # 使用与关键词统计相同的方法：清理文本后进行匹配
    clean_context = re.sub(r'[^\u4e00-\u9fa5]', '', context)

    found_keywords = []
    for keyword in keywords:
        if keyword in clean_context:
            found_keywords.append(keyword)
    return found_keywords


def generate_ai_response_stream(contexts, prompt, openai_config=None):
    """生成流式AI分析响应"""
    if not contexts:
        yield f"data: {json.dumps({'type': 'ai_chunk', 'data': '未找到相关上下文，无法进行分析。'})}\n\n"
        return

    # 检查是否有动态配置的OpenAI
    use_dynamic_config = (openai_config and
                         openai_config.get('api_key') and
                         openai_config.get('base_url'))

    # 检查是否有全局配置的OpenAI
    use_global_config = HAS_OPENAI and OPENAI_API_KEY

    print(f"🔧 流式OpenAI配置检查:")
    print(f"  - 动态配置可用: {use_dynamic_config}")
    print(f"  - 全局配置可用: {use_global_config}")

    # 如果有OpenAI配置，尝试使用真实的流式API
    if HAS_OPENAI and (use_dynamic_config or use_global_config):
        try:
            if use_dynamic_config:
                print("🤖 使用动态配置调用OpenAI流式API")
                yield from call_openai_api_stream_with_config(contexts, prompt, openai_config)
            else:
                print("🤖 使用全局配置调用OpenAI流式API")
                yield from call_openai_api_stream(contexts, prompt)
            return
        except Exception as e:
            print(f"❌ OpenAI流式API调用失败: {e}")
            yield f"data: {json.dumps({'type': 'status', 'message': 'OpenAI API调用失败，使用模拟响应'})}\n\n"

    # 使用模拟流式响应
    print("🤖 使用模拟流式响应")
    yield from generate_mock_ai_response_stream(contexts, prompt)


def generate_ai_response(contexts, prompt, openai_config=None):
    """生成AI分析响应（非流式，保持兼容性）"""
    if not contexts:
        return "未找到相关上下文，无法进行分析。"

    # 检查是否有动态配置的OpenAI
    use_dynamic_config = (openai_config and
                         openai_config.get('api_key') and
                         openai_config.get('base_url'))

    # 检查是否有全局配置的OpenAI
    use_global_config = HAS_OPENAI and OPENAI_API_KEY

    print(f"🔧 OpenAI配置检查:")
    print(f"  - 动态配置可用: {use_dynamic_config}")
    print(f"  - 全局配置可用: {use_global_config}")
    print(f"  - OpenAI库可用: {HAS_OPENAI}")

    # 如果有动态配置或全局配置了OpenAI，使用真实的AI分析
    if HAS_OPENAI and (use_dynamic_config or use_global_config):
        try:
            if use_dynamic_config:
                print("🤖 使用动态配置调用OpenAI API")
                return call_openai_api_with_config(contexts, prompt, openai_config)
            else:
                print("🤖 使用全局配置调用OpenAI API")
                return call_openai_api(contexts, prompt)
        except Exception as e:
            print(f"❌ OpenAI API调用失败: {e}")
            print("🔄 回退到模拟AI响应")
            # 如果API调用失败，回退到模拟响应
            return generate_mock_ai_response(contexts, prompt)
    else:
        # 使用模拟AI响应
        print("🤖 使用模拟AI响应")
        return generate_mock_ai_response(contexts, prompt)


def call_openai_api(contexts, prompt):
    """调用OpenAI API进行分析"""
    print(f"🤖 调用OpenAI API: {OPENAI_BASE_URL}, 模型: {OPENAI_MODEL}")
    print(f"📊 上下文统计: 总数={len(contexts)}")

    # 构建上下文内容，区分不同类型的上下文
    context_content = ""
    with_keywords_count = 0
    related_party_only_count = 0

    for i, ctx in enumerate(contexts, 1):
        context_type_label = ""
        if ctx.get('context_type') == 'with_keywords':
            with_keywords_count += 1
            context_type_label = "【包含关键词】"
        elif ctx.get('context_type') == 'related_party_only':
            related_party_only_count += 1
            context_type_label = "【仅关联方名称】"

        keywords_info = f"涉及关键词: {', '.join(ctx['keywords_found'])}" if ctx['keywords_found'] else "涉及关键词: 无"

        context_content += f"""
上下文 {i} {context_type_label}:
股票代码: {ctx['stock_code']}
公司名称: {ctx['company_name']}
关联方: {ctx['related_party']}
{keywords_info}
内容: {ctx['context']}

---
"""

    print(f"📈 上下文分类: 包含关键词={with_keywords_count}, 仅关联方={related_party_only_count}")
    print(f"📝 构建的上下文内容长度: {len(context_content)} 字符")

    # 构建系统提示词
    system_prompt = """
你是一位专业的财务分析师和商业关系专家，专门分析上市公司年报中关于供应商、客户协同合作的内容。

## 你的主要任务：
1. 仔细阅读和分析提供的年报内容上下文
2. 根据用户提出的问题，在年报中准确定位相关信息
3. 提供详细、准确的回答，重点关注供应商和客户协同合作方面

## 分析重点领域：
- **供应商关系管理**：供应商选择标准、合作模式、战略伙伴关系、供应链整合
- **客户关系管理**：主要客户构成、客户合作深度、长期合作协议、客户粘性
- **协同创新**：与供应商/客户的联合研发、技术合作、产品共同开发
- **供应链协同**：库存管理、物流协调、信息系统对接、供应链金融
- **战略合作**：股权投资、合资企业、战略联盟、生态圈建设
- **风险管控**：供应商/客户集中度风险、合作稳定性、应急预案

## 回答要求：
1. **准确性**：基于年报原文内容，不得编造信息
2. **完整性**：全面搜索相关章节（如经营情况讨论、重大合同、关联交易等）
3. **结构化**：按逻辑层次组织答案，使用清晰的段落和要点
4. **引用标注**：明确指出信息来源的年报章节或页码
5. **数据支撑**：提供具体的数字、比例、金额等量化信息
6. **趋势分析**：如有多年数据，分析变化趋势和发展方向

## 输出格式：
**直接回答**：简明扼要回答核心问题
**详细分析**：
- 相关背景信息
- 具体合作内容和模式
- 量化数据和指标
- 发展趋势和变化
**原文引用**：标注具体的年报章节和关键原文

如果年报中没有相关信息，请明确说明"年报中未披露相关信息"，不要推测或补充年报外的内容。

请用中文回答，结构清晰，分析深入。
"""

    # 构建用户提示词
    user_prompt = f"""请分析以下年报上下文内容：

{context_content}

分析要求：{prompt}

请提供详细的分析报告。"""

    try:
        # 构建请求参数
        request_params = {
            "model": OPENAI_MODEL,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "max_tokens": 2000,
            "temperature": 0.7
        }

        print("📤 OpenAI API 请求参数:")
        print(f"  - 模型: {request_params['model']}")
        print(f"  - 最大tokens: {request_params['max_tokens']}")
        print(f"  - 温度: {request_params['temperature']}")
        print(f"  - 系统提示词长度: {len(system_prompt)} 字符")
        print(f"  - 用户提示词长度: {len(user_prompt)} 字符")
        print(f"  - 系统提示词预览: {system_prompt[:200]}...")
        print(f"  - 用户提示词预览: {user_prompt[:200]}...")

        # 调用OpenAI API (兼容旧版本语法)
        try:
            print("🔄 使用旧版本OpenAI库语法...")
            response = openai.ChatCompletion.create(**request_params)
        except AttributeError:
            # 如果是新版本的openai库，使用新语法
            print("🔄 使用新版本OpenAI库语法...")
            from openai import OpenAI
            client = OpenAI(api_key=OPENAI_API_KEY, base_url=OPENAI_BASE_URL)
            response = client.chat.completions.create(**request_params)

        print("📥 OpenAI API 响应:")
        print(f"  - 响应类型: {type(response)}")

        # 提取响应内容
        if hasattr(response, 'choices') and len(response.choices) > 0:
            if hasattr(response.choices[0], 'message'):
                ai_response = response.choices[0].message.content.strip()
            else:
                ai_response = response.choices[0].get('message', {}).get('content', '').strip()
        else:
            ai_response = "API响应格式异常"

        print(f"  - 响应内容长度: {len(ai_response)} 字符")
        print(f"  - 响应内容预览: {ai_response[:300]}...")

        if not ai_response:
            print("⚠️ OpenAI API返回空响应")
            ai_response = "AI分析返回空结果，请检查API配置或重试。"

        print("✅ OpenAI API调用成功")
        return ai_response

    except Exception as e:
        print(f"❌ OpenAI API调用异常: {e}")
        print(f"❌ 异常类型: {type(e)}")
        print(f"❌ 异常详情: {str(e)}")
        raise e


def call_openai_api_with_config(contexts, prompt, config):
    """使用动态配置调用OpenAI API进行分析"""
    api_key = config.get('api_key')
    base_url = config.get('base_url', 'https://api.openai.com/v1')
    model = config.get('model', 'gpt-3.5-turbo')

    # 确保base_url格式正确
    if not base_url.startswith('http'):
        base_url = 'https://' + base_url

    if not base_url.endswith('/v1'):
        if base_url.endswith('/'):
            base_url = base_url + 'v1'
        else:
            base_url = base_url + '/v1'

    print(f"🤖 使用动态配置调用OpenAI API:")
    print(f"  - Base URL: {base_url}")
    print(f"  - 模型: {model}")
    print(f"  - API Key: {api_key[:10]}...{api_key[-4:] if len(api_key) > 14 else api_key}")
    print(f"  - 上下文数量: {len(contexts)}")

    # 构建上下文内容
    context_content = ""
    for i, ctx in enumerate(contexts, 1):
        context_content += f"""
上下文 {i}:
股票代码: {ctx['stock_code']}
公司名称: {ctx['company_name']}
关联方: {ctx['related_party']}
涉及关键词: {', '.join(ctx['keywords_found'])}
内容: {ctx['context']}

---
"""

    # 构建系统提示词
    system_prompt = f"""

你是一个专业的企业关系分析师，擅长分析上市公司年报中的协同创新关系。请基于提供的年报上下文内容，进行深入的分析。

上下文说明：
- 【包含关键词】的上下文：同时包含关联方名称和分析关键词，是重点分析对象
- 【仅关联方名称】的上下文：只包含关联方名称，可能包含其他形式的合作关系

本次分析共有 {len(contexts)} 个上下文，其中：
- 包含关键词的上下文：{with_keywords_count} 个
- 仅关联方名称的上下文：{related_party_only_count} 个

分析要求：
## 你的主要任务：
1. 仔细阅读和分析提供的年报内容上下文
2. 根据用户提出的问题，在年报中准确定位相关信息
3. 提供详细、准确的回答，重点关注供应商和客户协同合作方面

## 分析重点领域：
- **供应商关系管理**：供应商选择标准、合作模式、战略伙伴关系、供应链整合
- **客户关系管理**：主要客户构成、客户合作深度、长期合作协议、客户粘性
- **协同创新**：与供应商/客户的联合研发、技术合作、产品共同开发
- **供应链协同**：库存管理、物流协调、信息系统对接、供应链金融
- **战略合作**：股权投资、合资企业、战略联盟、生态圈建设
- **风险管控**：供应商/客户集中度风险、合作稳定性、应急预案

## 回答要求：
1. **准确性**：基于年报原文内容，不得编造信息
2. **完整性**：全面搜索相关章节（如经营情况讨论、重大合同、关联交易等）
3. **结构化**：按逻辑层次组织答案，使用清晰的段落和要点
4. **引用标注**：明确指出信息来源的年报章节或页码
5. **数据支撑**：提供具体的数字、比例、金额等量化信息
6. **趋势分析**：如有多年数据，分析变化趋势和发展方向

## 输出格式：
**直接回答**：简明扼要回答核心问题
**详细分析**：
- 相关背景信息
- 具体合作内容和模式
- 量化数据和指标
- 发展趋势和变化
**原文引用**：标注具体的年报章节和关键原文

如果年报中没有相关信息，请明确说明"年报中未披露相关信息"，不要推测或补充年报外的内容。

请用中文回答，结构清晰，分析深入。"""

    # 构建用户提示词
    user_prompt = f"""请分析以下年报上下文内容：

{context_content}

分析要求：{prompt}

请提供详细的分析报告。"""

    try:
        # 临时设置API配置
        original_api_key = getattr(openai, 'api_key', None)
        original_api_base = getattr(openai, 'api_base', None)

        openai.api_key = api_key
        openai.api_base = base_url

        # 构建请求参数
        request_params = {
            "model": model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "max_tokens": 2000,
            "temperature": 0.7
        }

        print("📤 动态配置OpenAI API 请求参数:")
        print(f"  - 模型: {request_params['model']}")
        print(f"  - 最大tokens: {request_params['max_tokens']}")
        print(f"  - 温度: {request_params['temperature']}")
        print(f"  - 系统提示词长度: {len(system_prompt)} 字符")
        print(f"  - 用户提示词长度: {len(user_prompt)} 字符")

        # 调用OpenAI API (兼容旧版本语法)
        try:
            print("🔄 使用旧版本OpenAI库语法（动态配置）...")
            response = openai.ChatCompletion.create(**request_params)
        except AttributeError:
            # 如果是新版本的openai库，使用新语法
            print("🔄 使用新版本OpenAI库语法（动态配置）...")
            from openai import OpenAI
            client = OpenAI(api_key=api_key, base_url=base_url)
            response = client.chat.completions.create(**request_params)

        print("📥 动态配置OpenAI API 响应:")
        print(f"  - 响应类型: {type(response)}")

        # 提取响应内容
        if hasattr(response, 'choices') and len(response.choices) > 0:
            if hasattr(response.choices[0], 'message'):
                ai_response = response.choices[0].message.content.strip()
            else:
                ai_response = response.choices[0].get('message', {}).get('content', '').strip()
        else:
            ai_response = "API响应格式异常"

        print(f"  - 响应内容长度: {len(ai_response)} 字符")
        print(f"  - 响应内容预览: {ai_response[:300]}...")

        if not ai_response:
            print("⚠️ 动态配置OpenAI API返回空响应")
            ai_response = "AI分析返回空结果，请检查API配置或重试。"

        print("✅ OpenAI API调用成功（动态配置）")

        # 恢复原始配置
        if original_api_key:
            openai.api_key = original_api_key
        if original_api_base:
            openai.api_base = original_api_base

        return ai_response

    except Exception as e:
        # 恢复原始配置
        if original_api_key:
            openai.api_key = original_api_key
        if original_api_base:
            openai.api_base = original_api_base

        print(f"❌ OpenAI API调用异常（动态配置）: {e}")
        raise e


def test_openai_connection(api_key, base_url, model):
    """测试OpenAI连接"""
    if not HAS_OPENAI:
        return {
            'success': False,
            'error': 'openai库未安装，请先安装：pip install openai'
        }

    try:
        # 验证输入参数
        if not api_key:
            return {
                'success': False,
                'error': 'API Key不能为空'
            }

        if not base_url:
            return {
                'success': False,
                'error': 'Base URL不能为空'
            }

        # 确保base_url格式正确
        if not base_url.startswith('http'):
            base_url = 'https://' + base_url

        if not base_url.endswith('/v1'):
            if base_url.endswith('/'):
                base_url = base_url + 'v1'
            else:
                base_url = base_url + '/v1'

        print(f"🔧 测试连接参数: API Key: {api_key[:10]}..., Base URL: {base_url}, Model: {model}")

        # 临时设置API配置
        original_api_key = getattr(openai, 'api_key', None)
        original_api_base = getattr(openai, 'api_base', None)

        openai.api_key = api_key
        openai.api_base = base_url

        print(f"🔧 设置openai.api_base = {base_url}")
        print(f"🔧 设置openai.api_key = {api_key[:10]}...")

        # 构建测试请求参数
        test_request_params = {
            "model": model,
            "messages": [
                {"role": "user", "content": "请回复'连接测试成功'"}
            ],
            "max_tokens": 50,
            "temperature": 0
        }

        print("📤 OpenAI连接测试请求参数:")
        print(f"  - 模型: {test_request_params['model']}")
        print(f"  - 最大tokens: {test_request_params['max_tokens']}")
        print(f"  - 测试消息: {test_request_params['messages'][0]['content']}")

        # 发送测试请求
        print("🔧 开始发送测试请求...")
        try:
            print("🔄 使用旧版本OpenAI库语法测试...")
            response = openai.ChatCompletion.create(**test_request_params)
        except AttributeError:
            # 如果是新版本的openai库，使用新语法
            print("🔄 使用新版本OpenAI库语法测试...")
            from openai import OpenAI
            client = OpenAI(api_key=api_key, base_url=base_url)
            response = client.chat.completions.create(**test_request_params)

        print("📥 OpenAI连接测试响应:")
        print(f"  - 响应类型: {type(response)}")

        # 提取响应内容
        if hasattr(response, 'choices') and len(response.choices) > 0:
            if hasattr(response.choices[0], 'message'):
                test_response = response.choices[0].message.content.strip()
            else:
                test_response = response.choices[0].get('message', {}).get('content', '').strip()
        else:
            test_response = "测试响应格式异常"

        print(f"  - 测试响应内容: {test_response}")

        if not test_response:
            print("⚠️ OpenAI连接测试返回空响应")
            test_response = "连接测试返回空结果"

        # 恢复原始配置
        if original_api_key:
            openai.api_key = original_api_key
        if original_api_base:
            openai.api_base = original_api_base

        return {
            'success': True,
            'response': test_response
        }

    except Exception as e:
        # 恢复原始配置
        if 'original_api_key' in locals() and original_api_key:
            openai.api_key = original_api_key
        if 'original_api_base' in locals() and original_api_base:
            openai.api_base = original_api_base

        error_msg = str(e)
        print(f"❌ OpenAI连接测试失败: {error_msg}")

        # 提供更友好的错误信息
        if "HTTP code 200" in error_msg or "<!doctype html>" in error_msg:
            error_msg = "API端点配置错误，返回了HTML页面而不是API响应。请检查Base URL是否正确。"
        elif "401" in error_msg or "Unauthorized" in error_msg:
            error_msg = "API Key无效或权限不足，请检查API Key是否正确。"
        elif "404" in error_msg or "Not Found" in error_msg:
            error_msg = "API端点未找到，请检查Base URL和模型名称是否正确。"
        elif "timeout" in error_msg.lower():
            error_msg = "连接超时，请检查网络连接和Base URL是否可访问。"

        return {
            'success': False,
            'error': error_msg
        }


def generate_mock_ai_response(contexts, prompt):
    """生成模拟AI分析响应"""
    # 统计信息
    stock_codes = set(ctx['stock_code'] for ctx in contexts)
    related_parties = set(ctx['related_party'] for ctx in contexts)
    all_keywords = set()

    # 分类统计
    with_keywords_contexts = [ctx for ctx in contexts if ctx.get('context_type') == 'with_keywords']
    related_party_only_contexts = [ctx for ctx in contexts if ctx.get('context_type') == 'related_party_only']

    for ctx in contexts:
        all_keywords.update(ctx['keywords_found'])

    # 生成分析报告
    response = f"""基于搜索到的 {len(contexts)} 个上下文片段，分析结果如下：

📊 **统计概览**
- 涉及股票代码：{len(stock_codes)} 个（{', '.join(sorted(stock_codes))}）
- 涉及关联方：{len(related_parties)} 个（{', '.join(sorted(related_parties))}）
- 发现关键词：{len(all_keywords)} 个（{', '.join(sorted(all_keywords))}）
- 包含关键词的上下文：{len(with_keywords_contexts)} 个
- 仅关联方名称的上下文：{len(related_party_only_contexts)} 个

🔍 **关系分析**
根据年报内容分析，发现以下协同创新关系：

"""

    # 按股票代码分组分析
    stock_analysis = {}
    for ctx in contexts:
        stock_code = ctx['stock_code']
        if stock_code not in stock_analysis:
            stock_analysis[stock_code] = {
                'company_name': ctx['company_name'],
                'related_parties': set(),
                'keyword_related_parties': set(),
                'name_only_related_parties': set(),
                'keywords': set()
            }

        stock_analysis[stock_code]['related_parties'].add(ctx['related_party'])
        stock_analysis[stock_code]['keywords'].update(ctx['keywords_found'])

        # 根据上下文类型分类关联方
        if ctx.get('context_type') == 'with_keywords':
            stock_analysis[stock_code]['keyword_related_parties'].add(ctx['related_party'])
        elif ctx.get('context_type') == 'related_party_only':
            stock_analysis[stock_code]['name_only_related_parties'].add(ctx['related_party'])

    for stock_code, analysis in stock_analysis.items():
        response += f"**{stock_code}（{analysis['company_name']}）**\n"

        # 分析包含关键词的关联方
        keyword_parties = analysis.get('keyword_related_parties', set())
        if keyword_parties:
            response += f"- 🎯 明确协同创新关联方：{', '.join(sorted(keyword_parties))}\n"

        # 分析仅关联方名称的关联方
        name_only_parties = analysis.get('name_only_related_parties', set())
        if name_only_parties:
            response += f"- 📋 潜在合作关联方：{', '.join(sorted(name_only_parties))}\n"

        response += f"- 涉及创新领域：{', '.join(sorted(analysis['keywords']))}\n\n"

    # 添加详细分析
    if with_keywords_contexts:
        response += f"""🎯 **明确协同创新关系分析**
基于 {len(with_keywords_contexts)} 个包含关键词的上下文，发现以下明确的协同创新关系：
"""
        for ctx in with_keywords_contexts[:3]:  # 显示前3个例子
            response += f"- {ctx['stock_code']} 与 {ctx['related_party']} 在 {', '.join(ctx['keywords_found'])} 方面存在合作\n"

    if related_party_only_contexts:
        response += f"""

📋 **潜在合作关系分析**
基于 {len(related_party_only_contexts)} 个仅包含关联方名称的上下文，发现以下潜在合作关系：
"""
        for ctx in related_party_only_contexts[:3]:  # 显示前3个例子
            response += f"- {ctx['stock_code']} 与 {ctx['related_party']} 存在业务往来，建议进一步分析合作性质\n"

    response += """

💡 **投资建议**
1. 重点关注明确协同创新关系的公司，这些合作通常具有更高的战略价值
2. 对于潜在合作关系，建议深入研究具体合作内容和商业模式
3. 关注协同创新对公司业绩和竞争力的实际影响
4. 建议进一步关注这些协同创新关系的发展，特别是在技术合作、研发投入、市场拓展等方面的具体合作内容和成效

⚠️ 注意：当前使用的是模拟AI响应。如需使用真实AI分析，请配置OpenAI API密钥。"""

    return response


def call_openai_api_stream(contexts, prompt):
    """使用全局配置调用OpenAI流式API"""
    yield from call_openai_api_stream_with_config(contexts, prompt, {
        'api_key': OPENAI_API_KEY,
        'base_url': OPENAI_BASE_URL,
        'model': OPENAI_MODEL
    })


def call_openai_api_stream_with_config(contexts, prompt, config):
    """使用动态配置调用OpenAI流式API"""
    import time

    api_key = config.get('api_key')
    base_url = config.get('base_url', 'https://api.openai.com/v1')
    model = config.get('model', 'gpt-3.5-turbo')

    # 构建上下文内容
    context_content = ""
    for i, ctx in enumerate(contexts, 1):
        context_content += f"""
上下文 {i}:
股票代码: {ctx['stock_code']}
公司名称: {ctx['company_name']}
关联方: {ctx['related_party']}
涉及关键词: {', '.join(ctx['keywords_found'])}
内容: {ctx['context']}

---
"""

    # 构建系统提示词
    system_prompt = """
你是一位专业的财务分析师和商业关系专家，专门分析上市公司年报中关于供应商、客户协同合作的内容。

## 你的主要任务：
1. 仔细阅读和分析提供的年报内容上下文
2. 根据用户提出的问题，在年报中准确定位相关信息
3. 提供详细、准确的回答，重点关注供应商和客户协同合作方面

## 分析重点领域：
- **供应商关系管理**：供应商选择标准、合作模式、战略伙伴关系、供应链整合
- **客户关系管理**：主要客户构成、客户合作深度、长期合作协议、客户粘性
- **协同创新**：与供应商/客户的联合研发、技术合作、产品共同开发
- **供应链协同**：库存管理、物流协调、信息系统对接、供应链金融
- **战略合作**：股权投资、合资企业、战略联盟、生态圈建设
- **风险管控**：供应商/客户集中度风险、合作稳定性、应急预案

## 回答要求：
1. **准确性**：基于年报原文内容，不得编造信息
2. **完整性**：全面搜索相关章节（如经营情况讨论、重大合同、关联交易等）
3. **结构化**：按逻辑层次组织答案，使用清晰的段落和要点
4. **引用标注**：明确指出信息来源的年报章节或页码
5. **数据支撑**：提供具体的数字、比例、金额等量化信息
6. **趋势分析**：如有多年数据，分析变化趋势和发展方向

## 输出格式：
**直接回答**：简明扼要回答核心问题
**详细分析**：
- 相关背景信息
- 具体合作内容和模式
- 量化数据和指标
- 发展趋势和变化
**原文引用**：标注具体的年报章节和关键原文

如果年报中没有相关信息，请明确说明"年报中未披露相关信息"，不要推测或补充年报外的内容。

请用中文回答，结构清晰，分析深入。

"""

    # 构建用户提示词
    user_prompt = f"""请分析以下年报上下文内容：

{context_content}

分析要求：{prompt}

请提供详细的分析报告。"""

    try:
        # 确保base_url格式正确
        if not base_url.startswith('http'):
            base_url = 'https://' + base_url
        if not base_url.endswith('/v1'):
            if base_url.endswith('/'):
                base_url = base_url + 'v1'
            else:
                base_url = base_url + '/v1'

        # 临时设置API配置
        original_api_key = getattr(openai, 'api_key', None)
        original_api_base = getattr(openai, 'api_base', None)

        openai.api_key = api_key
        openai.api_base = base_url

        # 构建流式请求参数
        stream_request_params = {
            "model": model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "max_tokens": 2000,
            "temperature": 0.7,
            "stream": True
        }

        print("📤 流式OpenAI API 请求参数:")
        print(f"  - 模型: {stream_request_params['model']}")
        print(f"  - 流式模式: {stream_request_params['stream']}")
        print(f"  - 系统提示词长度: {len(system_prompt)} 字符")
        print(f"  - 用户提示词长度: {len(user_prompt)} 字符")
        print(f"  - 总提示词长度: {len(system_prompt) + len(user_prompt)} 字符")

        # 检查提示词长度是否过长
        total_prompt_length = len(system_prompt) + len(user_prompt)
        if total_prompt_length > 8000:  # 大概的token限制检查
            print(f"⚠️ 提示词可能过长: {total_prompt_length} 字符，可能影响API响应")

        # 打印部分提示词内容用于调试
        print(f"  - 系统提示词开头: {system_prompt[:100]}...")
        print(f"  - 用户提示词开头: {user_prompt[:100]}...")

        # 尝试使用流式API
        try:
            # 对于旧版本的openai库，使用stream=True
            print("🔄 使用旧版本OpenAI库流式语法...")
            response = openai.ChatCompletion.create(**stream_request_params)

            print("📥 开始接收流式响应...")
            chunk_count = 0
            total_content = ""

            # 流式处理响应
            for chunk in response:
                chunk_count += 1
                print(f"📦 接收块 {chunk_count}: {chunk}")

                if 'choices' in chunk and len(chunk['choices']) > 0:
                    choice = chunk['choices'][0]
                    print(f"📦 块 {chunk_count} 选择内容: {choice}")

                    delta = choice.get('delta', {})
                    if 'content' in delta and delta['content']:
                        content = delta['content']
                        total_content += content
                        print(f"📦 块 {chunk_count} 内容: '{content}'")
                        yield f"data: {json.dumps({'type': 'ai_chunk', 'data': content})}\n\n"
                        time.sleep(0.01)  # 小延迟以控制流速
                    else:
                        print(f"📦 块 {chunk_count} 无内容或内容为空")
                else:
                    print(f"📦 块 {chunk_count} 无choices或choices为空")

            print(f"📥 流式响应完成: 接收 {chunk_count} 个块, 总内容长度 {len(total_content)} 字符")

            # 如果流式响应没有内容，尝试非流式API
            if chunk_count == 0 or len(total_content) == 0:
                print("⚠️ 流式API没有返回内容，尝试非流式API...")
                try:
                    non_stream_params = {
                        "model": model,
                        "messages": [
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_prompt}
                        ],
                        "max_tokens": 2000,
                        "temperature": 0.7
                    }

                    fallback_response = openai.ChatCompletion.create(**non_stream_params)
                    if hasattr(fallback_response, 'choices') and len(fallback_response.choices) > 0:
                        fallback_content = fallback_response.choices[0].message.content.strip()
                        if fallback_content:
                            print(f"✅ 非流式API返回内容: {len(fallback_content)} 字符")
                            # 模拟流式发送
                            chunk_size = 20
                            for i in range(0, len(fallback_content), chunk_size):
                                chunk = fallback_content[i:i + chunk_size]
                                yield f"data: {json.dumps({'type': 'ai_chunk', 'data': chunk})}\n\n"
                                time.sleep(0.02)
                        else:
                            print("❌ 非流式API也返回空内容")
                            yield f"data: {json.dumps({'type': 'ai_chunk', 'data': 'API返回空内容，请检查配置或重试。'})}\n\n"
                except Exception as fallback_error:
                    print(f"❌ 非流式API回退失败: {fallback_error}")
                    yield f"data: {json.dumps({'type': 'ai_chunk', 'data': f'API调用失败: {str(fallback_error)}'})}\n\n"

        except (AttributeError, TypeError):
            # 如果是新版本的openai库或不支持stream，回退到非流式
            try:
                print("🔄 使用新版本OpenAI库流式语法...")
                from openai import OpenAI
                client = OpenAI(api_key=api_key, base_url=base_url)

                response = client.chat.completions.create(
                    model=model,
                    messages=[
                        {"role": "system", "content": system_prompt},
                        {"role": "user", "content": user_prompt}
                    ],
                    max_tokens=2000,
                    temperature=0.7,
                    stream=True
                )

                print("📥 开始接收新版本流式响应...")
                chunk_count = 0
                total_content = ""

                # 流式处理响应
                for chunk in response:
                    chunk_count += 1
                    print(f"📦 新版本接收块 {chunk_count}: {chunk}")

                    if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                        choice = chunk.choices[0]
                        print(f"📦 新版本块 {chunk_count} 选择内容: {choice}")

                        if hasattr(choice, 'delta') and hasattr(choice.delta, 'content') and choice.delta.content:
                            content = choice.delta.content
                            total_content += content
                            print(f"📦 新版本块 {chunk_count} 内容: '{content}'")
                            yield f"data: {json.dumps({'type': 'ai_chunk', 'data': content})}\n\n"
                            time.sleep(0.01)
                        else:
                            print(f"📦 新版本块 {chunk_count} 无内容或内容为空")
                    else:
                        print(f"📦 新版本块 {chunk_count} 无choices或choices为空")

                print(f"📥 新版本流式响应完成: 接收 {chunk_count} 个块, 总内容长度 {len(total_content)} 字符")

                # 如果流式响应没有内容，尝试非流式API
                if chunk_count == 0 or len(total_content) == 0:
                    print("⚠️ 新版本流式API没有返回内容，尝试非流式API...")
                    try:
                        from openai import OpenAI
                        fallback_client = OpenAI(api_key=api_key, base_url=base_url)
                        fallback_response = fallback_client.chat.completions.create(
                            model=model,
                            messages=[
                                {"role": "system", "content": system_prompt},
                                {"role": "user", "content": user_prompt}
                            ],
                            max_tokens=2000,
                            temperature=0.7
                        )

                        if hasattr(fallback_response, 'choices') and len(fallback_response.choices) > 0:
                            fallback_content = fallback_response.choices[0].message.content.strip()
                            if fallback_content:
                                print(f"✅ 新版本非流式API返回内容: {len(fallback_content)} 字符")
                                # 模拟流式发送
                                chunk_size = 20
                                for i in range(0, len(fallback_content), chunk_size):
                                    chunk = fallback_content[i:i + chunk_size]
                                    yield f"data: {json.dumps({'type': 'ai_chunk', 'data': chunk})}\n\n"
                                    time.sleep(0.02)
                            else:
                                print("❌ 新版本非流式API也返回空内容")
                                yield f"data: {json.dumps({'type': 'ai_chunk', 'data': 'API返回空内容，请检查配置或重试。'})}\n\n"
                    except Exception as fallback_error:
                        print(f"❌ 新版本非流式API回退失败: {fallback_error}")
                        yield f"data: {json.dumps({'type': 'ai_chunk', 'data': f'API调用失败: {str(fallback_error)}'})}\n\n"

            except Exception as e:
                print(f"❌ 新版本流式API异常: {e}")
                # 如果流式API失败，使用模拟流式响应
                yield from generate_mock_ai_response_stream(contexts, prompt)

        # 恢复原始配置
        if original_api_key:
            openai.api_key = original_api_key
        if original_api_base:
            openai.api_base = original_api_base

    except Exception as e:
        # 恢复原始配置
        if 'original_api_key' in locals() and original_api_key:
            openai.api_key = original_api_key
        if 'original_api_base' in locals() and original_api_base:
            openai.api_base = original_api_base

        print(f"❌ OpenAI流式API调用失败: {e}")
        # 回退到模拟流式响应
        yield from generate_mock_ai_response_stream(contexts, prompt)


def generate_mock_ai_response_stream(contexts, prompt):
    """生成模拟流式AI响应"""
    import time

    print("🤖 开始生成模拟流式AI响应...")

    # 生成完整的模拟响应
    full_response = generate_mock_ai_response(contexts, prompt)

    print(f"📝 模拟AI响应生成完成，长度: {len(full_response)} 字符")
    print(f"📝 模拟AI响应预览: {full_response[:200]}...")

    if not full_response:
        print("⚠️ 模拟AI响应为空")
        yield f"data: {json.dumps({'type': 'ai_chunk', 'data': '模拟AI响应生成失败，请检查上下文数据。'})}\n\n"
        return

    # 将响应分块流式发送
    chunk_size = 10  # 每次发送的字符数
    chunk_count = 0

    for i in range(0, len(full_response), chunk_size):
        chunk = full_response[i:i + chunk_size]
        chunk_count += 1
        yield f"data: {json.dumps({'type': 'ai_chunk', 'data': chunk})}\n\n"
        time.sleep(0.05)  # 模拟网络延迟

    print(f"📤 模拟流式响应发送完成，共发送 {chunk_count} 个块")


if __name__ == '__main__':
    # 确保必要的目录存在
    os.makedirs('results/pdf', exist_ok=True)
    os.makedirs('results/txt', exist_ok=True)
    os.makedirs('exports', exist_ok=True)
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static/css', exist_ok=True)
    os.makedirs('static/js', exist_ok=True)

    app.run(debug=True, host='0.0.0.0', port=5000)
