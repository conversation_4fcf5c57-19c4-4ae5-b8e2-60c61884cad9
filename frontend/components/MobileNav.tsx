'use client'

import { useState, useEffect } from 'react'
import {
  Bar<PERSON>hart3,
  Search,
  Bot,
  Upload
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MobileNavProps {
  activeTab: string
  onTabChange: (tab: string) => void
}

const navigation = [
  {
    id: 'analysis',
    name: '在线',
    icon: BarChart3,
  },
  {
    id: 'keyword',
    name: '本地',
    icon: Search,
  },
  {
    id: 'ai',
    name: 'AI',
    icon: Bo<PERSON>,
  },
  {
    id: 'import',
    name: '导入',
    icon: Upload,
  }
]

export function MobileNav({ activeTab, onTabChange }: MobileNavProps) {
  const [animatingTab, setAnimatingTab] = useState<string | null>(null)

  const handleTabChange = (tabId: string) => {
    if (tabId !== activeTab) {
      setAnimatingTab(tabId)
      onTabChange(tabId)

      // 清除动画状态
      setTimeout(() => {
        setAnimatingTab(null)
      }, 300)
    }
  }

  return (
    <>




      {/* 移动端悬浮椭圆底部导航栏 */}
      <div className="lg:hidden fixed bottom-6 left-1/2 transform -translate-x-1/2 z-40 safe-area-pb float-animation">
        <div className="glass-effect rounded-full px-5 py-2 transition-all duration-500 hover:shadow-lg hover:shadow-black/10">
          <div className="flex items-center justify-center space-x-6">
            {navigation.map((item) => {
              const Icon = item.icon
              const isActive = activeTab === item.id

              return (
                <button
                  key={item.id}
                  onClick={() => handleTabChange(item.id)}
                  className={cn(
                    'relative flex flex-col items-center justify-center space-y-1 px-3 py-2 rounded-full transition-all duration-300 active:scale-90 min-w-[56px] group',
                    isActive
                      ? 'text-black bg-white/60 shadow-md backdrop-blur-sm transform scale-105 nav-item-active-animation'
                      : 'text-gray-600 hover:text-gray-800 hover:bg-white/30 hover:scale-105',
                    animatingTab === item.id && 'nav-item-active-animation'
                  )}
                >
                  <Icon className={cn(
                    'w-5 h-5 flex-shrink-0 transition-all duration-300',
                    isActive
                      ? 'transform scale-110 text-black icon-bounce-animation'
                      : 'group-hover:scale-110',
                    animatingTab === item.id && 'icon-bounce-animation'
                  )} />
                  <span className={cn(
                    'text-xs font-medium text-center leading-tight transition-all duration-300',
                    isActive
                      ? 'font-semibold transform scale-105'
                      : 'group-hover:font-semibold'
                  )}>
                    {item.name}
                  </span>

                  {/* 活跃状态指示器 */}
                  {isActive && (
                    <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-black rounded-full animate-pulse" />
                  )}

                  {/* 点击波纹效果 */}
                  {animatingTab === item.id && (
                    <div className="absolute inset-0 rounded-full bg-white/20 animate-ping" />
                  )}
                </button>
              )
            })}
          </div>
        </div>
      </div>
    </>
  )
}
