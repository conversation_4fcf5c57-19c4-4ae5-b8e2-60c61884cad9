'use client'

import { useState, useRef, useEffect } from 'react'
import {
  Upload,
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Folder,
  Database,
  Trash2,
  RefreshCw,
  HardDrive,
  Calendar,
  HelpCircle,
  Info
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'
import { apiMethods } from '@/lib/api'

interface FileItem {
  id: string
  name: string
  size: number
  status: 'pending' | 'uploading' | 'success' | 'error' | 'duplicate'
  progress?: number
  error?: string
}

interface DatabaseStats {
  total_reports: number
  total_companies: number
  unique_stock_codes: number
  total_file_size: number
  year_distribution: Record<string, number>
  stock_distribution: Record<string, number>
  recent_reports: any[]
}

export function DataImport() {
  const [files, setFiles] = useState<FileItem[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const [isLoadingStats, setIsLoadingStats] = useState(true)
  const [databaseStats, setDatabaseStats] = useState<DatabaseStats>({
    total_reports: 0,
    total_companies: 0,
    unique_stock_codes: 0,
    total_file_size: 0,
    year_distribution: {},
    stock_distribution: {},
    recent_reports: []
  })
  const [importStats, setImportStats] = useState({
    success: 0,
    error: 0,
    duplicate: 0
  })
  const [showFormatHelp, setShowFormatHelp] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const helpButtonRef = useRef<HTMLButtonElement>(null)
  const helpTooltipRef = useRef<HTMLDivElement>(null)

  // 获取数据库统计信息
  const fetchDatabaseStats = async () => {
    try {
      setIsLoadingStats(true)
      const response = await apiMethods.getDatabaseStats()
      if (response.data.success) {
        setDatabaseStats(response.data.data)
      }
    } catch (error) {
      console.error('获取数据库统计信息失败:', error)
    } finally {
      setIsLoadingStats(false)
    }
  }

  // 组件挂载时获取统计信息
  useEffect(() => {
    fetchDatabaseStats()
  }, [])

  // 点击外部关闭浮框
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node
      if (
        helpButtonRef.current &&
        helpTooltipRef.current &&
        !helpButtonRef.current.contains(target) &&
        !helpTooltipRef.current.contains(target)
      ) {
        setShowFormatHelp(false)
      }
    }

    if (showFormatHelp) {
      document.addEventListener('mousedown', handleClickOutside)
      return () => {
        document.removeEventListener('mousedown', handleClickOutside)
      }
    }
  }, [showFormatHelp])

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    const newFiles: FileItem[] = selectedFiles.map(file => ({
      id: Math.random().toString(36).substr(2, 9),
      name: file.name,
      size: file.size,
      status: 'pending'
    }))
    
    setFiles(prev => [...prev, ...newFiles])
  }

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault()
    const droppedFiles = Array.from(event.dataTransfer.files)
    const newFiles: FileItem[] = droppedFiles
      .filter(file => file.name.endsWith('.txt'))
      .map(file => ({
        id: Math.random().toString(36).substr(2, 9),
        name: file.name,
        size: file.size,
        status: 'pending'
      }))
    
    setFiles(prev => [...prev, ...newFiles])
  }

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault()
  }

  const startUpload = async () => {
    setIsUploading(true)
    const pendingFiles = files.filter(f => f.status === 'pending')

    let successCount = 0
    let errorCount = 0
    let duplicateCount = 0

    for (const file of pendingFiles) {
      // 更新状态为上传中
      setFiles(prev => prev.map(f =>
        f.id === file.id ? { ...f, status: 'uploading', progress: 0 } : f
      ))

      // 模拟上传进度
      for (let progress = 0; progress <= 100; progress += 20) {
        await new Promise(resolve => setTimeout(resolve, 200))
        setFiles(prev => prev.map(f =>
          f.id === file.id ? { ...f, progress } : f
        ))
      }

      // 模拟上传结果
      const random = Math.random()
      let status: FileItem['status']
      let error: string | undefined

      if (random < 0.1) {
        status = 'duplicate'
        duplicateCount++
      } else if (random < 0.2) {
        status = 'error'
        error = '文件格式不正确或内容无法解析'
        errorCount++
      } else {
        status = 'success'
        successCount++
      }

      setFiles(prev => prev.map(f =>
        f.id === file.id ? { ...f, status, error, progress: 100 } : f
      ))
    }

    // 更新导入统计
    setImportStats(prev => ({
      success: prev.success + successCount,
      error: prev.error + errorCount,
      duplicate: prev.duplicate + duplicateCount
    }))

    // 刷新数据库统计信息
    await fetchDatabaseStats()

    setIsUploading(false)
  }

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  const clearAll = () => {
    setFiles([])
    setImportStats({ success: 0, error: 0, duplicate: 0 })
  }

  const getStatusIcon = (status: FileItem['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'error':
        return <XCircle className="w-4 h-4 text-red-600" />
      case 'duplicate':
        return <AlertTriangle className="w-4 h-4 text-yellow-600" />
      case 'uploading':
        return <RefreshCw className="w-4 h-4 text-blue-600 animate-spin" />
      default:
        return <FileText className="w-4 h-4 text-gray-400" />
    }
  }

  const getStatusBadge = (status: FileItem['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="success" size="sm">成功</Badge>
      case 'error':
        return <Badge variant="error" size="sm">失败</Badge>
      case 'duplicate':
        return <Badge variant="warning" size="sm">重复</Badge>
      case 'uploading':
        return <Badge variant="info" size="sm">上传中</Badge>
      default:
        return <Badge variant="default" size="sm">等待</Badge>
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="space-y-6">
      {/* 数据库统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Database className="w-5 h-5 text-blue-600" />
                <div>
                  {isLoadingStats ? (
                    <div className="animate-pulse">
                      <div className="h-8 w-12 bg-gray-200 rounded mb-1"></div>
                      <div className="h-4 w-16 bg-gray-200 rounded"></div>
                    </div>
                  ) : (
                    <>
                      <p className="text-2xl font-bold text-gray-900">{databaseStats.total_reports}</p>
                      <p className="text-sm text-gray-500">总年报数</p>
                    </>
                  )}
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={fetchDatabaseStats}
                disabled={isLoadingStats}
              >
                <RefreshCw className={`w-4 h-4 ${isLoadingStats ? 'animate-spin' : ''}`} />
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{databaseStats.total_companies}</p>
                <p className="text-sm text-gray-500">公司数量</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <HardDrive className="w-5 h-5 text-purple-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{formatFileSize(databaseStats.total_file_size)}</p>
                <p className="text-sm text-gray-500">总文件大小</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center space-x-2">
              <Calendar className="w-5 h-5 text-orange-600" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{Object.keys(databaseStats.year_distribution).length}</p>
                <p className="text-sm text-gray-500">覆盖年份</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 本次导入统计 */}
      {(importStats.success > 0 || importStats.error > 0 || importStats.duplicate > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">本次导入统计</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-600" />
                <div>
                  <p className="text-xl font-bold text-gray-900">{importStats.success}</p>
                  <p className="text-sm text-gray-500">导入成功</p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <XCircle className="w-5 h-5 text-red-600" />
                <div>
                  <p className="text-xl font-bold text-gray-900">{importStats.error}</p>
                  <p className="text-sm text-gray-500">导入失败</p>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                <div>
                  <p className="text-xl font-bold text-gray-900">{importStats.duplicate}</p>
                  <p className="text-sm text-gray-500">重复跳过</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* 年份分布统计 */}
      {Object.keys(databaseStats.year_distribution).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">年报年份分布</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(databaseStats.year_distribution)
                .sort(([a], [b]) => parseInt(b) - parseInt(a))
                .slice(0, 10)
                .map(([year, count]) => {
                  const maxCount = Math.max(...Object.values(databaseStats.year_distribution))
                  const percentage = (count / maxCount) * 100

                  return (
                    <div key={year} className="flex items-center space-x-3">
                      <div className="w-12 text-sm font-medium text-gray-700">{year}</div>
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                      <div className="w-12 text-sm text-gray-600 text-right">{count}</div>
                    </div>
                  )
                })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 文件上传区域 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Upload className="w-5 h-5" />
            <span>文件导入</span>
            <div className="relative">
              <button
                ref={helpButtonRef}
                onClick={() => setShowFormatHelp(!showFormatHelp)}
                className={`ml-2 p-1 rounded-full transition-colors ${
                  showFormatHelp
                    ? 'bg-blue-100 text-blue-600'
                    : 'hover:bg-gray-100 text-gray-500'
                }`}
                title="查看文件格式要求"
              >
                <HelpCircle className="w-4 h-4" />
              </button>

              {/* 浮框提示 */}
              {showFormatHelp && (
                <div
                  ref={helpTooltipRef}
                  className="absolute left-0 top-8 z-50 w-80 sm:w-96 p-4 bg-white border border-gray-200 rounded-lg shadow-xl animate-in fade-in duration-200"
                >
                  <div className="flex items-start space-x-2">
                    <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-sm">
                      <h4 className="font-medium text-gray-900 mb-2">文件格式要求</h4>
                      <div className="space-y-2 text-gray-700">
                        <div className="flex items-start space-x-2">
                          <span className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span>文件格式：仅支持 <code className="bg-gray-100 px-1 rounded text-xs font-mono">.txt</code> 格式</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span>文件命名：建议使用 <code className="bg-gray-100 px-1 rounded text-xs font-mono">股票代码_公司名称_年报标题.txt</code> 格式</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span>文件内容：年报的纯文本内容，系统会自动提取股票代码和公司信息</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span>文件编码：UTF-8 编码（避免中文乱码）</span>
                        </div>
                        <div className="flex items-start space-x-2">
                          <span className="w-1 h-1 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span>重复检测：系统会自动检测并跳过已存在的文件</span>
                        </div>
                      </div>
                      <div className="mt-3 p-3 bg-blue-50 border border-blue-100 rounded">
                        <p className="font-medium text-blue-900 text-xs mb-2">示例文件名：</p>
                        <div className="space-y-1">
                          <p className="text-blue-700 font-mono text-xs bg-white px-2 py-1 rounded">000001_平安银行_2024年年度报告.txt</p>
                          <p className="text-blue-700 font-mono text-xs bg-white px-2 py-1 rounded">600036_招商银行_2024年年度报告.txt</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 浮框箭头 */}
                  <div className="absolute -top-2 left-6 w-4 h-4 bg-white border-l border-t border-gray-200 transform rotate-45"></div>
                </div>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div
            className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors cursor-pointer"
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onClick={() => fileInputRef.current?.click()}
          >
            <Folder className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              拖拽文件到此处或点击选择
            </h3>
            <p className="text-gray-500 mb-4">
              支持 .txt 格式的年报文件，支持批量上传
            </p>
            <Button variant="secondary">
              <Upload className="w-4 h-4 mr-2" />
              选择文件
            </Button>
          </div>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept=".txt"
            onChange={handleFileSelect}
            className="hidden"
          />
        </CardContent>
      </Card>

      {/* 文件列表 */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>文件列表 ({files.length})</CardTitle>
              <div className="flex items-center space-x-2">
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={clearAll}
                  disabled={isUploading}
                >
                  <Trash2 className="w-4 h-4 mr-1" />
                  清空
                </Button>
                <Button
                  onClick={startUpload}
                  disabled={isUploading || files.filter(f => f.status === 'pending').length === 0}
                  loading={isUploading}
                >
                  <Upload className="w-4 h-4 mr-1" />
                  开始导入
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {files.map((file) => (
                <div key={file.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3 flex-1">
                    {getStatusIcon(file.status)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {formatFileSize(file.size)}
                      </p>
                      {file.error && (
                        <p className="text-xs text-red-600 mt-1">{file.error}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    {file.status === 'uploading' && file.progress !== undefined && (
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${file.progress}%` }}
                        />
                      </div>
                    )}
                    
                    {getStatusBadge(file.status)}
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id)}
                      disabled={isUploading}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
