'use client'

import { useState, useEffect, useRef, memo } from 'react'
import { MarkdownRenderer } from './MarkdownRenderer'

interface StreamingMarkdownProps {
  content: string
  className?: string
  isStreaming?: boolean
  onComplete?: () => void
  showCursor?: boolean
}

/**
 * 真正的流式Markdown渲染器
 * 在流式过程中实时渲染markdown，并在末尾显示光标
 */
export const StreamingMarkdown = memo(function StreamingMarkdown({
  content,
  className = '',
  isStreaming = false,
  onComplete,
  showCursor = true
}: StreamingMarkdownProps) {
  const [displayContent, setDisplayContent] = useState('')
  const containerRef = useRef<HTMLDivElement>(null)
  const lastContentRef = useRef('')
  const animationFrameRef = useRef<number>()

  // 当内容变化时更新显示内容
  useEffect(() => {
    if (content !== lastContentRef.current) {
      lastContentRef.current = content
      setDisplayContent(content)

      // 自动滚动到底部
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }

      animationFrameRef.current = requestAnimationFrame(() => {
        if (containerRef.current) {
          containerRef.current.scrollTop = containerRef.current.scrollHeight
        }
      })
    }
  }, [content])

  // 流式完成回调
  useEffect(() => {
    if (!isStreaming) {
      onComplete?.()
    }
  }, [isStreaming, onComplete])

  // 清理动画帧
  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [])



  // Markdown渲染模式
  return (
    <div className={className}>
      <div
        ref={containerRef}
        className="bg-white rounded-lg border border-gray-200 overflow-hidden"
        style={{
          maxHeight: '400px',
          overflowY: 'auto'
        }}
      >
        <div className="p-3 sm:p-4 lg:p-6 relative">
          <div className="inline">
            <MarkdownRenderer content={displayContent} />
            {/* 流式状态下在末尾添加光标 - 黑色闪烁小球 */}
            {isStreaming && (
              <span
                className="inline-block w-2 h-2 bg-black rounded-full ml-1 cursor-blink"
                style={{
                  verticalAlign: 'baseline',
                  marginBottom: '1px', // 微调垂直位置
                  flexShrink: 0 // 防止光标被压缩
                }}
              />
            )}
          </div>
        </div>
      </div>
    </div>
  )
})

export default StreamingMarkdown
