'use client'

import { useState, useEffect } from 'react'
import { 
  Settings, 
  Key, 
  Globe, 
  Bot, 
  CheckCircle, 
  AlertCircle,
  Loader2
} from 'lucide-react'
import { Input } from './Input'
import { Button } from './Button'
import { Badge } from './Badge'
import { apiMethods } from '@/lib/api'

interface ModelSettings {
  useDefault: boolean
  apiKey: string
  baseUrl: string
  model: string
}

interface ModelConfigProps {
  settings: ModelSettings
  onChange: (settings: ModelSettings) => void
}

export function ModelConfig({ settings, onChange }: ModelConfigProps) {
  const [isTestingConnection, setIsTestingConnection] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle')
  const [connectionMessage, setConnectionMessage] = useState('')
  const [serverConfig, setServerConfig] = useState<any>(null)
  const [isLoadingConfig, setIsLoadingConfig] = useState(false)

  // 获取服务器配置
  const fetchServerConfig = async () => {
    try {
      setIsLoadingConfig(true)
      const response = await apiMethods.getAIConfig()
      if (response.data.success) {
        setServerConfig(response.data.data)
      }
    } catch (error) {
      console.error('获取服务器配置失败:', error)
    } finally {
      setIsLoadingConfig(false)
    }
  }

  // 组件挂载时获取服务器配置
  useEffect(() => {
    fetchServerConfig()
  }, [])

  const handleSettingChange = (key: keyof ModelSettings, value: any) => {
    onChange({
      ...settings,
      [key]: value
    })
    // 重置连接状态
    setConnectionStatus('idle')
    setConnectionMessage('')
  }

  const testConnection = async () => {
    setIsTestingConnection(true)
    setConnectionStatus('idle')
    setConnectionMessage('')

    try {
      const testParams = settings.useDefault 
        ? { use_default: true }
        : {
            use_default: false,
            api_key: settings.apiKey,
            base_url: settings.baseUrl,
            model: settings.model
          }

      const response = await apiMethods.testOpenAI(testParams)
      
      if (response.data.success) {
        setConnectionStatus('success')
        setConnectionMessage('连接测试成功！')
      } else {
        setConnectionStatus('error')
        setConnectionMessage(response.data.error || '连接测试失败')
      }
    } catch (error: any) {
      setConnectionStatus('error')
      setConnectionMessage(error.response?.data?.message || error.message || '连接测试失败')
    } finally {
      setIsTestingConnection(false)
    }
  }

  const commonModels = [
    'gpt-3.5-turbo',
    'gpt-4',
    'gpt-4-turbo',
    'gpt-4o',
    'gpt-4o-mini'
  ]

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Settings className="w-4 h-4 text-gray-600" />
          <h4 className="font-medium text-gray-900">OpenAI 模型配置</h4>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={fetchServerConfig}
          disabled={isLoadingConfig}
        >
          <RefreshCw className={`w-4 h-4 ${isLoadingConfig ? 'animate-spin' : ''}`} />
        </Button>
      </div>

      {/* 服务器配置状态 */}
      {serverConfig && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg mb-4">
          <div className="flex items-center space-x-2 mb-2">
            <Info className="w-4 h-4 text-blue-600" />
            <span className="text-sm font-medium text-blue-900">服务器配置状态</span>
          </div>
          <div className="text-xs text-blue-800 space-y-1">
            <div>OpenAI库: {serverConfig.has_openai ? '✅ 已安装' : '❌ 未安装'}</div>
            <div>API密钥: {serverConfig.default_config.api_key_configured ? '✅ 已配置' : '❌ 未配置'}</div>
            <div>默认模型: {serverConfig.default_config.model}</div>
            <div>Base URL: {serverConfig.default_config.base_url}</div>
          </div>
        </div>
      )}

      {/* 使用默认配置选项 */}
      <div className="flex items-center space-x-3">
        <input
          type="checkbox"
          id="useDefault"
          checked={settings.useDefault}
          onChange={(e) => handleSettingChange('useDefault', e.target.checked)}
          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />
        <label htmlFor="useDefault" className="text-sm font-medium text-gray-700">
          使用系统默认配置
        </label>
        <Badge variant="outline" size="sm">推荐</Badge>
      </div>

      {/* 自定义配置 */}
      {!settings.useDefault && (
        <div className="space-y-4 pl-6 border-l-2 border-gray-200">
          {/* API Key */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Key className="w-4 h-4 inline mr-1" />
              API Key
            </label>
            <Input
              type="password"
              placeholder="sk-..."
              value={settings.apiKey}
              onChange={(e) => handleSettingChange('apiKey', e.target.value)}
              className="font-mono text-sm"
            />
          </div>

          {/* Base URL */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Globe className="w-4 h-4 inline mr-1" />
              Base URL
            </label>
            <Input
              placeholder="https://api.openai.com/v1"
              value={settings.baseUrl}
              onChange={(e) => handleSettingChange('baseUrl', e.target.value)}
              className="font-mono text-sm"
            />
            <p className="text-xs text-gray-500 mt-1">
              留空使用默认地址，或填入代理地址
            </p>
          </div>

          {/* Model */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              <Bot className="w-4 h-4 inline mr-1" />
              模型
            </label>
            <div className="space-y-2">
              <Input
                placeholder="gpt-3.5-turbo"
                value={settings.model}
                onChange={(e) => handleSettingChange('model', e.target.value)}
                className="font-mono text-sm"
              />
              <div className="flex flex-wrap gap-1">
                {commonModels.map((model) => (
                  <Button
                    key={model}
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSettingChange('model', model)}
                    className={`text-xs h-6 px-2 ${
                      settings.model === model ? 'bg-blue-100 text-blue-700' : ''
                    }`}
                  >
                    {model}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 连接测试 */}
      <div className="pt-4 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={testConnection}
            disabled={isTestingConnection || (!settings.useDefault && (!settings.apiKey || !settings.model))}
            loading={isTestingConnection}
          >
            {isTestingConnection ? (
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="w-4 h-4 mr-2" />
            )}
            测试连接
          </Button>

          {connectionStatus !== 'idle' && (
            <div className="flex items-center space-x-2">
              {connectionStatus === 'success' ? (
                <CheckCircle className="w-4 h-4 text-green-500" />
              ) : (
                <AlertCircle className="w-4 h-4 text-red-500" />
              )}
              <span className={`text-sm ${
                connectionStatus === 'success' ? 'text-green-600' : 'text-red-600'
              }`}>
                {connectionMessage}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* 配置说明 */}
      <div className="text-xs text-gray-500 bg-blue-50 p-3 rounded">
        <p className="font-medium mb-1">配置说明：</p>
        <ul className="space-y-1">
          <li>• 推荐使用系统默认配置，已预配置最佳参数</li>
          <li>• 自定义配置需要有效的OpenAI API Key</li>
          <li>• 支持OpenAI官方API和兼容的第三方API</li>
          <li>• 建议使用gpt-3.5-turbo或gpt-4模型</li>
        </ul>
      </div>
    </div>
  )
}
