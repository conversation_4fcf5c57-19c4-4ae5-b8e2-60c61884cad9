'use client'

import { useState, useRef, useEffect } from 'react'
import { flushSync } from 'react-dom'
import {
  Bo<PERSON>,
  Send,
  Sparkles,
  AlertCircle,
  CheckCircle,
  Clock,
  MessageSquare,
  Settings,
  Database,
  Users,
  Hash,
  ChevronDown,
  ChevronUp,
  Quote
} from 'lucide-react'
import { Card, CardHeader, CardTitle, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { Textarea, Input } from './ui/Input'
import { Badge } from './ui/Badge'
import { Loading } from './ui/Loading'
import { ModelConfigModal } from './ui/ModelConfigModal'
import { ContextModal } from './ui/ContextModal'
import { MarkdownRenderer } from './ui/MarkdownRenderer'
import { StreamingMarkdownRenderer } from './ui/StreamingMarkdownRenderer'
import { StreamingRenderer } from './ui/StreamingTextRenderer'
import { RealTimeTextDisplay, RealTimeTextDisplayWithMetrics } from './ui/RealTimeTextDisplay'
import StreamingMarkdown from './ui/StreamingMarkdown'
import { apiMethods } from '@/lib/api'
import { cacheUtils, type AIAnalysisParams, type ModelConfig as ModelConfigType, type AIAnalysisHistoryItem } from '@/lib/cache'
import { debugStream } from '../utils/streamingDebug'

interface AIAnalysisProps {
  onAnalysisStart?: (content: string) => void
}

interface AnalysisParams {
  stockCodes: string
  keywords: string
  relatedParties: string
  prompt: string
}

interface ModelSettings {
  useDefault: boolean
  apiKey: string
  baseUrl: string
  model: string
}

export function AIAnalysis({ onAnalysisStart }: AIAnalysisProps) {
  // 从缓存加载初始数据
  const loadCachedParams = () => {
    const cached = cacheUtils.getAIAnalysisParams()
    return cached || {
      stockCodes: '',
      keywords: '',
      relatedParties: '',
      prompt: ''
    }
  }

  const loadCachedModelConfig = () => {
    const cached = cacheUtils.getModelConfig()
    return cached || {
      useDefault: true,
      apiKey: '',
      baseUrl: '',
      model: 'gpt-3.5-turbo'
    }
  }

  // 分析参数状态
  const [analysisParams, setAnalysisParams] = useState<AnalysisParams>(loadCachedParams)

  // 模型配置状态
  const [modelSettings, setModelSettings] = useState<ModelSettings>(loadCachedModelConfig)

  // UI状态
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [isStreaming, setIsStreaming] = useState(false)
  const [analysisResults, setAnalysisResults] = useState<string>('')
  const [analysisHistory, setAnalysisHistory] = useState<AIAnalysisHistoryItem[]>([])
  const [showModelConfigModal, setShowModelConfigModal] = useState(false)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [analysisStartTime, setAnalysisStartTime] = useState<number>(0)
  const [streamingContent, setStreamingContent] = useState<string>('')

  // 上下文数据状态
  const [analysisContexts, setAnalysisContexts] = useState<any[]>([])
  const [showContextModal, setShowContextModal] = useState(false)

  // 流式结果显示
  const resultsRef = useRef<HTMLDivElement>(null)

  // 自动滚动到底部
  useEffect(() => {
    if (resultsRef.current) {
      resultsRef.current.scrollTop = resultsRef.current.scrollHeight
    }
  }, [analysisResults])

  // 缓存分析参数
  useEffect(() => {
    cacheUtils.saveAIAnalysisParams(analysisParams)
  }, [analysisParams])

  // 缓存模型配置
  useEffect(() => {
    cacheUtils.saveModelConfig(modelSettings)
  }, [modelSettings])

  // 加载历史记录
  useEffect(() => {
    const history = cacheUtils.getAIAnalysisHistory()
    setAnalysisHistory(history)
  }, [])

  // 监听上下文数据变化
  useEffect(() => {
    console.log('🔄 analysisContexts状态变化:', analysisContexts.length, '个上下文')
  }, [analysisContexts])

  const handleAnalysis = async () => {
    if (!analysisParams.stockCodes.trim() || !analysisParams.keywords.trim() ||
        !analysisParams.prompt.trim()) {
      alert('请填写必需的分析参数（股票代码、关键词、分析要求）')
      return
    }

    setIsAnalyzing(true)
    setIsStreaming(true)
    setAnalysisResults('')
    setStreamingContent('')
    setAnalysisContexts([]) // 重置上下文数据
    setIsCollapsed(true) // 开始分析时折叠输入面板
    setAnalysisStartTime(Date.now())
    onAnalysisStart?.(analysisParams.prompt)

    // 开始调试
    debugStream.start()

    try {
      // 准备请求参数
      const requestParams = {
        stock_codes: analysisParams.stockCodes,
        keywords: analysisParams.keywords,
        related_parties: analysisParams.relatedParties,
        prompt: analysisParams.prompt,
        openai_config: modelSettings.useDefault ? {} : {
          api_key: modelSettings.apiKey,
          base_url: modelSettings.baseUrl,
          model: modelSettings.model
        }
      }

      // 调用流式AI分析API
      const response = await fetch('http://localhost:5000/api/ai_analysis_stream', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestParams)
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      let accumulatedResult = ''

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'ai_chunk' && data.data) {
                accumulatedResult += data.data

                // 调试记录（只记录新增的chunk，不是累积内容）
                debugStream.chunk(data.data, 'ai_chunk')

                // 更新流式内容
                setStreamingContent(accumulatedResult)

                // 实时更新分析结果，用于组件显示
                setAnalysisResults(accumulatedResult)

                // 使用requestAnimationFrame优化滚动
                requestAnimationFrame(() => {
                  if (resultsRef.current) {
                    resultsRef.current.scrollTop = resultsRef.current.scrollHeight
                  }
                })
              } else if (data.type === 'status') {
                console.log('状态更新:', data.message)
              } else if (data.type === 'contexts') {
                console.log('🔍 接收到上下文数据:', data.data?.length || 0, '个')
                console.log('📋 上下文详情:', data.data)
                // 保存上下文数据
                const contexts = data.data || []
                setAnalysisContexts(contexts)
                console.log('💾 已保存上下文数据到状态, 长度:', contexts.length)
                // 强制重新渲染检查
                setTimeout(() => {
                  console.log('🔄 状态检查 - analysisContexts长度应该是:', contexts.length)
                }, 100)
              } else if (data.type === 'complete') {
                console.log('分析完成:', data.message)
                // 流式完成，更新状态
                setIsStreaming(false)
                setAnalysisResults(accumulatedResult)
              } else if (data.type === 'error') {
                throw new Error(data.message)
              }
            } catch (e) {
              // 忽略JSON解析错误，继续处理下一行
              console.warn('解析流数据失败:', line, e)
            }
          }
        }
      }

      // 保存到历史记录
      const duration = Date.now() - analysisStartTime
      const historyItem: AIAnalysisHistoryItem = {
        id: Date.now(),
        params: analysisParams,
        result: accumulatedResult,
        timestamp: new Date(),
        duration
      }

      // 保存到缓存和本地状态
      cacheUtils.saveAIAnalysisHistory(historyItem)
      setAnalysisHistory(prev => [historyItem, ...prev])

    } catch (error: any) {
      console.error('AI分析失败:', error)
      const errorMessage = `分析失败: ${error.message}`
      setAnalysisResults(errorMessage)

      // 即使失败也保存到历史记录
      const duration = Date.now() - analysisStartTime
      const historyItem: AIAnalysisHistoryItem = {
        id: Date.now(),
        params: analysisParams,
        result: errorMessage,
        timestamp: new Date(),
        duration
      }
      cacheUtils.saveAIAnalysisHistory(historyItem)
      setAnalysisHistory(prev => [historyItem, ...prev])
    } finally {
      setIsAnalyzing(false)
      setIsStreaming(false)

      // 结束调试
      debugStream.end()
    }
  }

  return (
    <div className="space-y-4 lg:space-y-6">
      {/* 分析界面 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
        {/* 输入区域 */}
        <Card className={`transition-all duration-300 ${isCollapsed ? 'lg:col-span-1' : 'lg:col-span-1'}`}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2">
                <MessageSquare className="w-5 h-5" />
                <span>AI分析配置</span>
              </CardTitle>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCollapsed(!isCollapsed)}
                className="lg:hidden"
              >
                {isCollapsed ? <ChevronDown className="w-4 h-4" /> : <ChevronUp className="w-4 h-4" />}
              </Button>
            </div>
          </CardHeader>
          <CardContent className={`space-y-3 sm:space-y-4 ${isCollapsed ? 'lg:block hidden' : 'block'}`}>
            {/* 股票代码输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Database className="w-4 h-4 inline mr-1" />
                股票代码 (每行一个)
              </label>
              <Textarea
                placeholder="例如：&#10;000001&#10;000002&#10;600000"
                value={analysisParams.stockCodes}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, stockCodes: e.target.value }))}
                className="min-h-[60px] sm:min-h-[80px] text-sm"
              />
            </div>

            {/* 关键词输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Hash className="w-4 h-4 inline mr-1" />
                统计关键词 (每行一个)
              </label>
              <Textarea
                placeholder="例如：&#10;创新&#10;研发&#10;技术合作"
                value={analysisParams.keywords}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, keywords: e.target.value }))}
                className="min-h-[60px] sm:min-h-[80px] text-sm"
              />
            </div>

            {/* 关联方输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Users className="w-4 h-4 inline mr-1" />
                关联方 (每行一个，选填)
                <span className="text-xs text-gray-500 ml-2">可选项</span>
              </label>
              <Textarea
                placeholder="例如：&#10;清华大学&#10;中科院&#10;华为技术&#10;&#10;如不需要关联方分析可留空"
                value={analysisParams.relatedParties}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, relatedParties: e.target.value }))}
                className="min-h-[50px] sm:min-h-[80px] text-sm"
              />
            </div>

            {/* AI分析要求 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Bot className="w-4 h-4 inline mr-1" />
                AI分析要求
              </label>
              <Textarea
                placeholder="请描述您希望AI分析的具体内容，例如：分析企业的协同创新情况，重点关注与高校和科研院所的合作..."
                value={analysisParams.prompt}
                onChange={(e) => setAnalysisParams(prev => ({ ...prev, prompt: e.target.value }))}
                className="min-h-[80px] sm:min-h-[100px] text-sm"
              />
            </div>

            {/* 模型配置 */}
            <div>
              <Button
                variant="outline"
                onClick={() => setShowModelConfigModal(true)}
                className="w-full"
              >
                <Settings className="w-4 h-4 mr-2" />
                模型配置
              </Button>
            </div>

            {/* 开始分析按钮 */}
            <Button
              onClick={handleAnalysis}
              disabled={isAnalyzing || !analysisParams.stockCodes.trim() || !analysisParams.keywords.trim() ||
                       !analysisParams.prompt.trim()}
              loading={isAnalyzing}
              className="w-full h-10 sm:h-11 text-sm sm:text-base"
            >
              <Send className="w-4 h-4 mr-2" />
              {isAnalyzing ? 'AI分析中...' : '开始AI分析'}
            </Button>
          </CardContent>
        </Card>

        {/* 分析结果 */}
        <Card className="lg:col-span-1 flex flex-col" style={{ minHeight: '680px' }}>
          <CardHeader className="flex-shrink-0">
            <CardTitle className="flex items-center space-x-2">
              <Bot className="w-5 h-5" />
              <span>AI 分析结果</span>
              {/* 调试信息 - 开发时显示 */}
              {process.env.NODE_ENV === 'development' && (
                <>
                  <span className="text-xs text-gray-400 ml-2">
                    (上下文: {analysisContexts.length})
                  </span>
                  <button
                    onClick={() => console.log('🔍 当前状态:', { analysisContexts: analysisContexts.length, analysisResults: !!analysisResults, isAnalyzing })}
                    className="text-xs text-blue-500 ml-1 underline"
                  >
                    调试
                  </button>
                </>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowContextModal(true)}
                disabled={analysisContexts.length === 0}
                className="ml-2 p-1 h-auto"
                title={analysisContexts.length > 0 ? `查看引用上下文 (${analysisContexts.length}个)` : '暂无引用上下文'}
              >
                <Quote className={`w-4 h-4 ${analysisContexts.length > 0 ? 'text-gray-500 hover:text-blue-600' : 'text-gray-300'}`} />
              </Button>
              {isAnalyzing && (
                <Badge variant="default" className="ml-auto">
                  分析中...
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col overflow-hidden">
            {isAnalyzing && !isStreaming ? (
              <div className="flex flex-col items-center justify-center py-8">
                <Loading size="lg" text="AI正在分析中..." />
                <p className="text-sm text-gray-500 mt-4 text-center">
                  正在获取年报数据并进行AI分析<br />
                  这可能需要几分钟时间，请耐心等待
                </p>
              </div>
            ) : (isStreaming || analysisResults) ? (
              <div className="flex flex-col h-full">
                {/* 流式Markdown显示 */}
                <div ref={resultsRef} className="flex-1 min-h-0">
                  <StreamingMarkdown
                    content={isStreaming ? streamingContent : analysisResults}
                    isStreaming={isStreaming}
                    showCursor={true}
                    onComplete={() => {
                      console.log('AI分析完成')
                      // 可以在这里添加完成后的处理逻辑
                    }}
                  />
                </div>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center py-12 text-center">
                <Bot className="w-12 h-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  等待AI分析
                </h3>
                <p className="text-gray-500 max-w-sm">
                  请在左侧配置股票代码、关键词、关联方和分析要求，AI将基于本地年报数据为您提供深度分析
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* 分析历史 */}
      {analysisHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="w-5 h-5" />
              <span>分析历史</span>
              <Badge variant="outline" size="sm" className="ml-auto">
                {analysisHistory.length} 条记录
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 max-h-[400px] overflow-y-auto">
              {analysisHistory.map((item) => (
                <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <Badge variant="default" size="sm">
                        {new Date(item.timestamp).toLocaleString()}
                      </Badge>
                      {item.duration && (
                        <Badge variant="outline" size="sm">
                          {(item.duration / 1000).toFixed(1)}秒
                        </Badge>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setAnalysisParams(item.params)
                        setAnalysisResults(item.result)
                      }}
                    >
                      查看详情
                    </Button>
                  </div>

                  <div className="space-y-2 text-sm">
                    <div>
                      <span className="font-medium text-gray-700">股票代码:</span>
                      <span className="text-gray-600 ml-2">{item.params.stockCodes.split('\n').slice(0, 3).join(', ')}</span>
                      {item.params.stockCodes.split('\n').length > 3 && (
                        <span className="text-gray-500"> 等{item.params.stockCodes.split('\n').length}个</span>
                      )}
                    </div>
                    <div>
                      <span className="font-medium text-gray-700">关键词:</span>
                      <span className="text-gray-600 ml-2">{item.params.keywords.split('\n').slice(0, 3).join(', ')}</span>
                      {item.params.keywords.split('\n').length > 3 && (
                        <span className="text-gray-500"> 等{item.params.keywords.split('\n').length}个</span>
                      )}
                    </div>
                    {item.params.relatedParties && (
                      <div>
                        <span className="font-medium text-gray-700">关联方:</span>
                        <span className="text-gray-600 ml-2">{item.params.relatedParties.split('\n').slice(0, 2).join(', ')}</span>
                        {item.params.relatedParties.split('\n').length > 2 && (
                          <span className="text-gray-500"> 等{item.params.relatedParties.split('\n').length}个</span>
                        )}
                      </div>
                    )}
                    <div>
                      <span className="font-medium text-gray-700">分析要求:</span>
                      <span className="text-gray-600 ml-2 line-clamp-2">{item.params.prompt}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* 模型配置模态框 */}
      <ModelConfigModal
        isOpen={showModelConfigModal}
        onClose={() => setShowModelConfigModal(false)}
        settings={modelSettings}
        onChange={setModelSettings}
      />

      {/* 上下文查看模态框 */}
      <ContextModal
        isOpen={showContextModal}
        onClose={() => setShowContextModal(false)}
        contexts={analysisContexts}
      />
    </div>
  )
}
