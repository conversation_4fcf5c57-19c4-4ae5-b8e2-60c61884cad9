import axios from 'axios'

// 创建axios实例
export const api = axios.create({
  baseURL: '/api', // 使用Next.js的rewrite代理
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 可以在这里添加认证token等
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    // 统一错误处理
    if (error.response?.status === 500) {
      console.error('服务器内部错误')
    } else if (error.response?.status === 404) {
      console.error('接口不存在')
    } else if (error.code === 'ECONNABORTED') {
      console.error('请求超时')
    }
    return Promise.reject(error)
  }
)

// API方法
export const apiMethods = {
  // 健康检查
  healthCheck: () => api.get('/health'),

  // 获取公司列表
  getCompanies: () => api.get('/companies'),

  // 根据股票代码获取公司信息
  getCompanyByCode: (stockCode: string) => api.get(`/companies/${stockCode}`),

  // 导入TXT文件
  importTxtFiles: (txtDir: string) =>
    api.post('/import_txt', { txt_dir: txtDir }),

  // 搜索年报
  searchReports: (params: {
    keyword?: string
    stock_codes?: string[]
    year?: number
  }) => api.post('/search_reports', params),

  // 获取数据库统计信息
  getDatabaseStats: () => api.get('/database_stats'),

  // 开始分析任务
  startAnalysis: (params: {
    stock_codes: string
    keywords: string
    search_keyword?: string
    start_date?: string
    end_date?: string
    use_online?: boolean
    related_parties?: string
  }) => api.post('/start_analysis', params),

  // 关键词分析
  keywordAnalysis: (params: {
    stock_codes: string
    keywords: string
    related_parties?: string
  }) => api.post('/keyword_analysis', params),
  
  // 获取任务状态
  getTaskStatus: (taskId: string) => api.get(`/task_status/${taskId}`),

  // 停止任务
  stopTask: (taskId: string) => api.post(`/stop_task/${taskId}`),

  // 获取关键词上下文
  getKeywordContext: (params: {
    analysis_id: string
    keyword: string
    context_length?: number
    stock_code_filter?: string
    file_name_filter?: string
  }) => api.get(`/keyword_context/${params.analysis_id}/${params.keyword}`, {
    params: {
      context_length: params.context_length,
      stock_code: params.stock_code_filter,
      file_name: params.file_name_filter
    }
  }),
  
  // 获取分析结果
  getAnalysisResults: (taskId: string) => api.get(`/analysis_results/${taskId}`),
  
  // 导出结果
  exportResults: (taskId: string) => api.get(`/export_results/${taskId}`, {
    responseType: 'blob'
  }),
  
  // 导出筛选结果
  exportFilteredResults: (params: {
    data: any[]
    filters: any
    format: 'excel' | 'csv'
    timestamp: string
  }) => api.post('/export_filtered_results', params, {
    responseType: 'blob'
  }),
  

  
  // AI分析
  aiAnalysis: (params: {
    stock_codes: string
    keywords: string
    related_parties: string
    prompt: string
    openai_config?: any
  }) => api.post('/ai_analysis', params),

  // 流式AI分析
  aiAnalysisStream: (params: {
    stock_codes: string
    keywords: string
    related_parties: string
    prompt: string
    openai_config?: any
  }) => {
    return fetch(`${API_BASE_URL}/ai_analysis_stream`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    })
  },

  // 测试OpenAI连接
  testOpenAI: (params: {
    use_default?: boolean
    api_key?: string
    base_url?: string
    model?: string
  }) => api.post('/test_openai', params),

  // 获取AI配置信息
  getAIConfig: () => api.get('/config'),
  
  // 清理重复数据
  cleanDuplicates: () => api.post('/clean_duplicates'),
  
  // 调试数据库
  debugDatabase: () => api.get('/debug_database'),
}
