# OpenAI API 配置
# 请在此处填入您的OpenAI API密钥
OPENAI_API_KEY=sk-wpudnqlixz1nNRGfOUDPr0UXqsEre6y0EZB3jGjGdQvinkgl

# OpenAI API基础URL
OPENAI_BASE_URL=https://new-api.bybing.me/v1

# OpenAI 模型名称
OPENAI_MODEL=claude-sonnet-4

# 数据库配置
DATABASE_PATH=data/database/cninfo.db

# 文件存储配置
TXT_DIR=txt
PDF_DIR=pdf
EXPORTS_DIR=exports
RESULTS_DIR=results

# Flask应用配置
FLASK_DEBUG=True
FLASK_PORT=5000
FLASK_HOST=0.0.0.0

# 日志配置
LOG_LEVEL=INFO

# 爬虫配置
REQUEST_TIMEOUT=30
REQUEST_RETRIES=3
REQUEST_DELAY=1

# AI分析配置
CONTEXT_LENGTH=300
MAX_CONTEXTS=100
AI_ANALYSIS_TIMEOUT=120
